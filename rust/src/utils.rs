use std::cmp;
use std::collections::HashMap;
use std::io::{
    self,
    Write,
};
use std::mem::zeroed;

use chrono::{
    NaiveDate,
    NaiveTime,
    Datelike,
};
use libc::{
    ioctl,
    winsize,
    STDOUT_FILENO,
    TIOCGWINSZ,
};
use regex::Regex;

use crate::utils_classes::{
    MYSQLConfig,
    MYSQLValue,
};


// __HAS_TEST__
pub fn all_values_are_0<K>(dictionary: &HashMap<K, i32>) -> bool
where
    K: std::hash::Hash + Eq,
{
    !dictionary.values().any(|&v| v != 0)
}


pub fn hms_to_hourkey(hms: &str) -> String {
    // __HAS_TEST__

    let parts: Vec<&str> = hms.split(':').collect();
    let hh = parts.get(0).unwrap_or(&"00");
    format!("{}:00 - {}:59", hh, hh)
}


pub fn get_terminal_width() -> usize {
    unsafe {
        let mut ws: winsize = zeroed();
        if ioctl(STDOUT_FILENO, TIOCGWINSZ, &mut ws) == 0 && ws.ws_col > 0 {
            ws.ws_col as usize
        } else {
            0
        }
    }
}


pub fn separator() -> String {
    let width = get_terminal_width();
    "-".repeat(width)
}


pub fn normalize_date(date: &str) -> String {
    // __HAS_TEST__

    // first try parsing 2-digit year format with manual fix
    if let Ok(parsed) = NaiveDate::parse_from_str(date, "%m/%d/%y") {
        let year = parsed.year();
        let fixed_year = if year < 100 {
            if year >= 70 { 1900 + year } else { 2000 + year }
        } else {
            year
        };

        if let Some(fixed_date) = NaiveDate::from_ymd_opt(fixed_year, parsed.month(), parsed.day()) {
            return fixed_date.format("%Y-%m-%d").to_string();
        }
    }

    // then try parsing 4-digit year normally
    if let Ok(parsed) = NaiveDate::parse_from_str(date, "%m/%d/%Y") {
        return parsed.format("%Y-%m-%d").to_string();
    }

    // if all formats fail,
    // return the original date string
    date.to_string()
}


pub fn normalize_dns_question_name(url: &str) -> String {
    // __HAS_TEST__

    let re = Regex::new(r"\([0-9]+\)").unwrap();
    let normalized = re.replace_all(url, ".");
    normalized.trim_matches('.').to_string()
}


pub fn normalize_time(time: &str) -> String {
    // __HAS_TEST__

    // try parsing with 12-hour format with AM/PM (%I:%M:%S %p)
    if let Ok(t) = NaiveTime::parse_from_str(time, "%I:%M:%S %p") {
        return t.format("%H:%M:%S").to_string();
    }

    // try parsing with 24-hour format with AM/PM (%H:%M:%S %p)
    if let Ok(t) = NaiveTime::parse_from_str(time, "%H:%M:%S %p") {
        return t.format("%H:%M:%S").to_string();
    }

    // if all formats fail,
    // return the original time string
    time.to_string()
}


pub fn underscore_to_dash(string: &str) -> String {
    // __HAS_TEST__

    string.replace("_", "-")
}


pub fn dash_to_underscore(string: &str) -> String {
    // __HAS_TEST__

    if string.is_empty() {
        return String::new();
    }

    string.replace("-", "_")
}


pub fn create_name_of_database(slug: &str, ymd: &str, object_name: &str) -> String {
    // __HAS_TEST__
    // __DATABASE_YMD_PATTERN__

    // Check if slug is in NON_DATED_DATABASES
    if let MYSQLValue::List(non_dated_dbs) = MYSQLConfig::NON_DATED_DATABASES.value() {
        if non_dated_dbs.contains(&slug.to_string()) {
            return slug.to_string();
        }
    }

    // get the DB_NAME_SEPARATOR
    let db_name_separator = match MYSQLConfig::DB_NAME_SEPARATOR.value() {
        MYSQLValue::Str(sep) => sep,
        _ => panic!("Error getting DB_NAME_SEPARATOR"),
    };

    if object_name.is_empty() {
        format!("{}{}{}", slug, db_name_separator, dash_to_underscore(ymd))
    } else {
        format!(
            "{}{}{}{}{}",
            slug,
            db_name_separator,
            dash_to_underscore(object_name),
            db_name_separator,
            dash_to_underscore(ymd),
        )
    }
}


pub fn evenly_sized_batches(total_length: isize, len_of_each_batch: Option<usize>) -> Vec<Vec<usize>> {
    // __HAS_TEST__

    if total_length <= 0 {
        return Vec::new();
    }

    // __SET_DEFAULT_VALUE__
    let len_of_each_batch_ = len_of_each_batch.unwrap_or(10);

    let mut batches = Vec::new();
    let range: Vec<usize> = (1..=(total_length as usize)).collect();

    for i in (0..range.len()).step_by(len_of_each_batch_) {
        let end = std::cmp::min(i + len_of_each_batch_, range.len());
        batches.push(range[i..end].to_vec());
    }

    batches
}


pub fn create_path_of_infile(database_name: &str, table_name: &str, chunk_number: Option<usize>) -> String {
    // __HAS_TEST__
    // __DATABASE_YMD_PATTERN__

    // __SET_DEFAULT_VALUE__
    let chunk_number_ = chunk_number.unwrap_or(0);

    let _suff = if chunk_number_ == 0 {
        String::new()
    } else {
        format!("__chunk_{}", chunk_number_)
    };

    format!("/tmp/infile__{}__{}{}.csv", database_name, table_name, _suff)
}


pub fn get_no_of_infiles(length: usize) -> usize {
    // __HAS_TEST__

    if length == 0 {
        return 0;
    }

    let infile_chunksize = if let MYSQLValue::Int(size) = MYSQLConfig::INFILE_CHUNKSIZE.value() {
        size as usize
    } else {
        panic!("Error getting INFILE_CHUNKSIZE from MYSQLConfig");
    };

    if length <= infile_chunksize {
        return 1;
    }

    let mut no_of_chunks = length / infile_chunksize;
    if length % infile_chunksize != 0 {
        no_of_chunks += 1;
    }

    no_of_chunks
}


pub fn create_name_of_index(key: &str) -> String {
    // __HAS_TEST__

    let re = Regex::new(r"[^0-9a-zA-Z]+").unwrap();
    format!("{}_index", re.replace_all(&key.to_lowercase(), ""))
}
