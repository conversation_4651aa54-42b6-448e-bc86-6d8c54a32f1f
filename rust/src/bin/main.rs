// use eterna::utils_classes::{
//     // *Config,
//     MYSQLValue,
//     MYSQLConfig,
//     GeoLocationConfig,
//     MaliciousConfig,
//     DaemonConfig,
//     DHCPConfig,
//     DNSConfig,
//     FilterLogConfig,
//     RouterConfig,
//     RouterBoardConfig,
//     SnortConfig,
//     SquidConfig,
//     SwitchConfig,
//     UserAuditConfig,
//     UserNoticeConfig,
//     UserWarningConfig,
//     VMwareConfig,
//     VPNServerConfig,
//     WindowsServerConfig,

//     // *Parser,
//     Parser,
//     DaemonParser,
//     DHCPParser,
// };


// use dotenv::from_path;
// use std::env;
// use std::path::Path;


fn main() {
    /*
    // load .env
    let env_path = Path::new("../.env");
    from_path(env_path).expect("Failed to load .env");

    let host = env::var("MYSQL_DATADIR").unwrap();
    println!("MYSQL_DATADIR: {}", host);
    // MYSQL_DATADIR: /var/lib/mysql
    */

    // -------------------------------

    /*
    println!("\nutils_patterns.rs");

    let line = "2023-05-13 09:51:58 Sensor-1 (auth/info) [(squid-1)]";

    if let Some(caps) = utils_patterns::INIT_REG.captures(line) {
        println!("Date: {}", &caps[1]);
        println!("Time: {}", &caps[2]);
        println!("Sensor: {}", &caps[3]);
        // Date: 2023-05-13
        // Time: 09:51:58
        // Sensor: Sensor-1
    }

    if let Some(caps) = utils_patterns::EVENT_REG.captures(line) {
        println!("Event: {}", &caps[1]);
        // Event: (auth/info)
    }

    if let Some(caps) = utils_patterns::ALERT_REG.captures(line) {
        println!("Alert: {}", &caps[1]);
        // Alert: [(squid-1)]
    }
    */

    /*
    // -------------------------------
    // *Config

    println!("\nMYSQLConfig");

    match MYSQLConfig::DEFAULT_DATA_TYPE.value() {
        MYSQLValue::Str(val) => println!("DEFAULT_DATA_TYPE: {}", val),
        _ => (),
    }
    // DEFAULT_DATA_TYPE: MEDIUMTEXT

    match MYSQLConfig::BUILTIN_DATABASES.value() {
        MYSQLValue::List(dbs) => println!("BUILTIN_DATABASES: {:?}", dbs),
        _ => (),
    }
    // BUILTIN_DATABASES: ["information_schema", "mysql", "performance_schema", "sys"]

    if let MYSQLValue::Str(val) = MYSQLConfig::MYSQL_HOST.value() {
        println!("MYSQL_HOST: {}", val);
    }
    // MYSQL_HOST: localhost

    let in_st = MYSQLConfig::get_infile_statement();
    println!("Infile Statement: {}", in_st);
    // Infile Statement: LOAD DATA LOCAL INFILE



    println!("\nGeoLocationConfig");

    // get string
    let title = GeoLocationConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: GeoLocation
    }

    // get list
    let db_headers__domain = GeoLocationConfig::DB_HEADERS__DOMAIN.value();
    if let MYSQLValue::List(db_headers__domain__list) = db_headers__domain {
        println!("DB_HEADERS__DOMAIN: {:?}", db_headers__domain__list);
        // DB_HEADERS__DOMAIN: ["ID", "Domain", "Country", ...]
    }

    // get string
    let db_columns__domain = GeoLocationConfig::DB_COLUMNS__DOMAIN.value();
    if let MYSQLValue::Str(db_columns__domain__str) = db_columns__domain {
        println!("DB_COLUMNS__DOMAIN:\n{}", db_columns__domain__str);
        // DB_COLUMNS__DOMAIN:
        // ID             INT PRIMARY KEY AUTO_INCREMENT,
        // Domain         MEDIUMTEXT,
        // Country        MEDIUMTEXT,
        // `Country Code` MEDIUMTEXT,
        // ...
    }

    let table_name = GeoLocationConfig::get_table_name("ip");
    println!("Table name (ip): {}", table_name);
    // Table name (ip): geolocationtable__ip

    let logs_parsed_dir = GeoLocationConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/geolocation

    // let select_statement = GeoLocationConfig::get_select_statement("domain");
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM geolocationtable__domain



    println!("\nMaliciousConfig");

    // get string
    let title = MaliciousConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: Malicious
    }

    // get list
    let db_headers__domain = MaliciousConfig::DB_HEADERS__DOMAIN.value();
    if let MYSQLValue::List(db_headers__domain__list) = db_headers__domain {
        println!("DB_HEADERS__DOMAIN: {:?}", db_headers__domain__list);
        // DB_HEADERS__DOMAIN: ["ID", "Domain", "Sources"]
    }

    // get string
    let db_columns__domain = MaliciousConfig::DB_COLUMNS__DOMAIN.value();
    if let MYSQLValue::Str(db_columns__domain__str) = db_columns__domain {
        println!("DB_COLUMNS__DOMAIN:\n{}", db_columns__domain__str);
        // DB_COLUMNS__DOMAIN:
        // ID      INT PRIMARY KEY AUTO_INCREMENT,
        // Domain  VARCHAR(5000),
        // Sources MEDIUMTEXT
    }

    let table_name = MaliciousConfig::get_table_name("ip", true);
    println!("Table name (ip, parent): {}", table_name);
    // Table name (ip, parent): malicioustable__ip__parent

    let table_name = MaliciousConfig::get_table_name("ip", false);
    println!("Table name (ip): {}", table_name);
    // Table name (ip): malicioustable__ip

    let logs_parsed_dir = MaliciousConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/malicious

    // let select_statement = MaliciousConfig::get_select_statement("domain", false);
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM malicioustable__domain



    println!("\nDaemonConfig");

    // get string
    let title = DaemonConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: Daemon
    }

    // get string
    let filterby = DaemonConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filterby {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY: (daemon/
    }

    // get bool
    let filterby_is_in_alert_type = DaemonConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = DaemonConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: ["(daemon/alert)", "(daemon/crit)", "(daemon/debug)", ...]
    }

    // get string
    let db_columns = DaemonConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Level        MEDIUMTEXT,
        // ...
    }

    let table_name = DaemonConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: daemontable

    let logs_parsed_dir = DaemonConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/daemon

    // let select_statement = DaemonConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM daemontable



    println!("\nDHCPConfig");

    // get string
    let title = DHCPConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: DHCP
    }

    // get string
    let filter_by = DHCPConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY: [dhcp]
    }

    // get bool
    let filterby_is_in_alert_type = DHCPConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: true
    }

    // get list
    let event_types = DHCPConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: ["(syslog/info)", "(user/notice)"]
    }

    // get string
    let db_columns = DHCPConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Event ID     MEDIUMTEXT,
        // ...
    }

    let table_name = DHCPConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: dhcptable

    let logs_parsed_dir = DHCPConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/dhcp

    // let select_statement = DHCPConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM dhcptable



    println!("\nDNSConfig");

    // get string
    let title = DNSConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: DNS
    }

    // get string
    let filter_by = DNSConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY: [dns]
    }

    // get bool
    let filterby_is_in_alert_type = DNSConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: true
    }

    // get list
    let event_types = DNSConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: ["(syslog/info)", "(user/notice)"]
    }

    // get string
    let db_columns = DNSConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Thread ID    MEDIUMTEXT,
        // ...
    }

    let table_name = DNSConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: dnstable

    let logs_parsed_dir = DNSConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/dns

    // let select_statement = DNSConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM dnstable



    println!("\nFilterLogConfig");

    // get string
    let title = FilterLogConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: FilterLog
    }

    // get string
    let filter_by = FilterLogConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY: [filterlog]
    }

    // get bool
    let filterby_is_in_alert_type = FilterLogConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: true
    }

    // get list
    let event_types = FilterLogConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: []
    }

    // get string
    let db_columns = FilterLogConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID            INT PRIMARY KEY AUTO_INCREMENT,
        // Date          VARCHAR(10),
        // Time          VARCHAR(13),
        // Protocol Name MEDIUMTEXT,
        // ...
    }

    let table_name = FilterLogConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: filterlogtable

    let logs_parsed_dir = FilterLogConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/filterlog

    // let select_statement = FilterLogConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM filterlogtable



    println!("\nRouterConfig");

    // get string
    let title = RouterConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: Router
    }

    // get string
    let filter_by = RouterConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY:
    }

    // get bool
    let filterby_is_in_alert_type = RouterConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = RouterConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: ["(local7/alert)", "(local7/crit)", "(local7/debug)", ...]
    }

    // get string
    let db_columns = RouterConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Level        MEDIUMTEXT,
        // ...
    }

    let table_name = RouterConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: routertable

    let logs_parsed_dir = RouterConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/router

    // let select_statement = RouterConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM routertable



    println!("\nRouterBoardConfig");

    // get string
    let title = RouterBoardConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: RouterBoard
    }

    // get string
    let filter_by = RouterBoardConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY:
    }

    // get bool
    let filterby_is_in_alert_type = RouterBoardConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = RouterBoardConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: ["(local7/alert)", "(local7/crit)", "(local7/debug)", ...]
    }

    // get string
    let db_columns = RouterBoardConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Level        MEDIUMTEXT,
        // ...
    }

    let table_name = RouterBoardConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: routerboardtable

    let logs_parsed_dir = RouterBoardConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/routerboard

    // let select_statement = RouterBoardConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM routerboardtable



    println!("\nSnortConfig");

    // get string
    let title = SnortConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: Snort
    }

    // get string
    let filter_by = SnortConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY: [snort]
    }

    // get bool
    let filterby_is_in_alert_type = SnortConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: true
    }

    // get list
    let event_types = SnortConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: ["(auth/alert)"]
    }

    // get string
    let db_columns = SnortConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // `GID:SID`    MEDIUMTEXT,
        // ...
    }

    let table_name = SnortConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: snorttable

    let logs_parsed_dir = SnortConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/snort

    // let select_statement = SnortConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM snorttable



    // get dict
    let classifications_dict = SnortConfig::CLASSIFICATIONS_DICT.value();
    if let MYSQLValue::ClassificationDict(classifications_dict__dict) = classifications_dict {
        println!("CLASSIFICATIONS_DICT: {:?}", classifications_dict__dict);
        // CLASSIFICATIONS_DICT: {
        //     "Denial of Service": ClassificationInfo { classtype: "successful-dos", priority: "medium", index: 2 },
        //     "Misc activity": ClassificationInfo { classtype: "misc-activity", priority: "low", index: 3 },
        //     ...
        // }
    }

    // get list
    let classifications__criticals = SnortConfig::CLASSIFICATIONS__CRITICALS.value();
    if let MYSQLValue::List(classifications__criticals__list) = classifications__criticals {
        println!("CLASSIFICATIONS__CRITICALS: {:?}", classifications__criticals__list);
        // CLASSIFICATIONS__CRITICALS: ["Attempted Administrator Privilege Gain", "Attempted User Privilege Gain", ...]
    }



    println!("\nSquidConfig");

    // get string
    let title = SquidConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: Squid
    }

    // get string
    let filter_by = SquidConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY: [(squid-1)]
    }

    // get bool
    let filterby_is_in_alert_type = SquidConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: true
    }

    // get list
    let event_types = SquidConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: []
    }

    // get string
    let db_columns = SquidConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID            INT PRIMARY KEY AUTO_INCREMENT,
        // Date          VARCHAR(10),
        // Time          VARCHAR(13),
        // Duration      MEDIUMTEXT,
        // ...
    }

    let table_name = SquidConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: squidtable

    let logs_parsed_dir = SquidConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/squid

    // let select_statement = SquidConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM squidtable



    println!("\nSwitchConfig");

    // get string
    let title = SwitchConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: Switch
    }

    // get string
    let filter_by = SwitchConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY:
    }

    // get bool
    let filterby_is_in_alert_type = SwitchConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = SwitchConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: ["(local7/alert)", "(local7/crit)", "(local7/debug)", ...]
    }

    // get string
    let db_columns = SwitchConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Level        MEDIUMTEXT,
        // ...
    }

    let table_name = SwitchConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: switchtable

    let logs_parsed_dir = SwitchConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/switch

    // let select_statement = SwitchConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM switchtable



    println!("\nUserAuditConfig");

    // get string
    let title = UserAuditConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: User Audit
    }

    // get string
    let filter_by = UserAuditConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY:
    }

    // get bool
    let filterby_is_in_alert_type = UserAuditConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = UserAuditConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: ["(daemon/err)", "(auth/emerg)"]
    }

    // get string
    let db_columns = UserAuditConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // `Alert Type` MEDIUMTEXT,
        // ...
    }

    let table_name = UserAuditConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: useraudittable

    let logs_parsed_dir = UserAuditConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/useraudit

    // let select_statement = UserAuditConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM useraudittable



    println!("\nUserNoticeConfig");

    // get string
    let title = UserNoticeConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: User Notice
    }

    // get string
    let filter_by = UserNoticeConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY: [openvpn]
    }

    // get bool
    let filterby_is_in_alert_type = UserNoticeConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: true
    }

    // get list
    let event_types = UserNoticeConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: ["(user/notice)"]
    }

    // get string
    let db_columns = UserNoticeConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID               INT PRIMARY KEY AUTO_INCREMENT,
        // Date             VARCHAR(10),
        // Time             VARCHAR(13),
        // Server           MEDIUMTEXT,
        // ...
    }

    let table_name = UserNoticeConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: usernoticetable

    let logs_parsed_dir = UserNoticeConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/usernotice

    // let select_statement = UserNoticeConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM usernoticetable



    println!("\nUserWarningConfig");

    // get string
    let title = UserWarningConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: User Warning
    }

    // get string
    let filter_by = UserWarningConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY: (user/warning)
    }

    // get bool
    let filterby_is_in_alert_type = UserWarningConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = UserWarningConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: ["(user/warning)"]
    }

    // get string
    let db_columns = UserWarningConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // `Alert Type` MEDIUMTEXT,
        // ...
    }

    let table_name = UserWarningConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: userwarningtable

    let logs_parsed_dir = UserWarningConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/userwarning

    // let select_statement = UserWarningConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM userwarningtable



    println!("\nVMwareConfig");

    // get string
    let title = VMwareConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: VMware
    }

    // get string
    let filter_by = VMwareConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY:
    }

    // get bool
    let filterby_is_in_alert_type = VMwareConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = VMwareConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: []
    }

    // get string
    let db_columns = VMwareConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Level        MEDIUMTEXT,
        // ...
    }

    let table_name = VMwareConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: vmwaretable

    let logs_parsed_dir = VMwareConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/vmware

    // let select_statement = VMwareConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM vmwaretable



    println!("\nVPNServerConfig");

    // get string
    let title = VPNServerConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: VPN Server
    }

    // get string
    let filter_by = VPNServerConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY:
    }

    // get bool
    let filterby_is_in_alert_type = VPNServerConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = VPNServerConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: []
    }

    // get string
    let db_columns = VPNServerConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Domain       MEDIUMTEXT,
        // ...
    }

    let table_name = VPNServerConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: vpnservertable

    let logs_parsed_dir = VPNServerConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/vpnserver

    // let select_statement = VPNServerConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM vpnservertable



    println!("\nWindowsServerConfig");

    // get string
    let title = WindowsServerConfig::TITLE.value();
    if let MYSQLValue::Str(title__str) = title {
        println!("TITLE: {}", title__str);
        // TITLE: Windows Server
    }

    // get string
    let filter_by = WindowsServerConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby__str) = filter_by {
        println!("FILTERBY: {}", filterby__str);
        // FILTERBY:
    }

    // get bool
    let filterby_is_in_alert_type = WindowsServerConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type__bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type__bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = WindowsServerConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(event_types__list) = event_types {
        println!("EVENT_TYPES: {:?}", event_types__list);
        // EVENT_TYPES: []
    }

    // get string
    let db_columns = WindowsServerConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(db_columns__str) = db_columns {
        println!("DB_COLUMNS:\n{}", db_columns__str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Alert Type   MEDIUMTEXT,
        // ...
    }

    let table_name = WindowsServerConfig::get_table_name(Some(""));
    println!("Table name: {}", table_name);
    // Table name: windowsservertable

    let table_name = WindowsServerConfig::get_table_name(Some("accountlogon"));
    println!("Table name (accountlogon): {}", table_name);
    // Table name (accountlogon): windowsservertable__accountlogon

    let logs_parsed_dir = WindowsServerConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/windowsserver

    // let select_statement = WindowsServerConfig::get_select_statement(Some(""));
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM windowsservertable

    // let select_statement = WindowsServerConfig::get_select_statement(Some("accountlogon"));
    // println!("Select statement (accountlogon): {}", select_statement);
    // Select statement (accountlogon): SELECT * FROM windowsservertable__accountlogon

    let category = WindowsServerConfig::get_category_from_event_id("4625");
    println!("Category (for event id: 4625): {}", category);
    // Category (for event id: 4625): Logon/Logoff

    let category = WindowsServerConfig::get_category_from_event_id("9999");
    println!("Category (for event id: 9999): {}", category);
    // Category (for event id: 9999):



    let ei__am = WindowsServerConfig::EVENT_IDS__PRIVILEGE_USE.value();
    if let MYSQLValue::List(ei__am__list) = ei__am {
        println!("EVENT_IDS__PRIVILEGE_USE: {:?}", ei__am__list);
        // EVENT_IDS__PRIVILEGE_USE: ["4727", "4740", "4725", ...]
    }

    // -------------------------------
    // *Parser

    println!("\nDaemonParser");

    let mut instance = DaemonParser::new(
        DaemonConfig::SLUG.value_string(),
        "2020-01-02".to_string(),
        "Sensor-1".to_string(),
    );
    println!("instance.slug: {}", instance.slug);
    println!("instance.object_name: {}", instance.object_name);
    println!("instance.no_of_rows(): {}", instance.no_of_rows());
    // instance.slug: daemon
    // instance.object_name: Sensor-1
    // instance.no_of_rows(): 0

    // add rows
    instance.rows.push(vec![
        "2020-01-02".to_string(),
        "01:00:00".to_string(),
        "row1".to_string(),
        "message1".to_string(),
    ]);
    instance.rows.push(vec![
        "2020-01-02".to_string(),
        "01:00:00".to_string(),
        "row2".to_string(),
        "message2".to_string(),
    ]);

    println!("instance.no_of_rows() after adding: {}", instance.no_of_rows());
    println!("First row: {:?}", instance.rows.get(0));
    // instance.no_of_rows() after adding: 2
    // First row: Some(("2020-01-02", "01:00:00", "row1", "message1"))

    instance.truncate_rows();
    println!("instance.no_of_rows() after truncating: {}", instance.no_of_rows());
    // instance.no_of_rows() after truncating: 0



    println!("\nDHCPParser");

    let mut instance = DHCPParser::new(
        DHCPConfig::SLUG.value_string(),
        "2020-01-02".to_string(),
    );
    println!("instance.slug: {}", instance.slug);
    println!("instance.no_of_rows(): {}", instance.no_of_rows());
    // instance.slug: dhcp
    // instance.no_of_rows(): 0

    println!("instance.times_and_counts: {:?}", instance.times_and_counts);
    // instance.times_and_counts: {"23:00 - 23:59": 0, "14:00 - 14:59": 0, ...}

    // update value of key in dictionary
    //
    // method 1: only if key exists
    if let Some(count) = instance.times_and_counts.get_mut("14:00 - 14:59") {
        *count = 42;
        println!("Updated '14:00 - 14:59'. Count: {}", count);
    }
    // method 2: if key does not exist, it will be added
    instance.times_and_counts.insert("09:00 - 09:59".to_string(), 100);
    println!("Updated/Added '09:00 - 09:59'. Count: {}", 100);
    */

    println!("\n✅ Success");
}
