use std::cmp::Ordering;
use std::collections::HashMap;
use std::env;
use std::f64;
use std::fmt;
use std::io::Write;

use indexmap::IndexMap;
use once_cell::sync::Lazy;
use regex::Regex;


pub static INT_OR_FLOAT_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"^[0-9\.]+$").unwrap()
});

pub static TRAILING_ZERO_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"\.0+$").unwrap()
});


pub fn is_int_or_float<S: ToString>(input: S) -> bool {
    INT_OR_FLOAT_PATTERN.is_match(&input.to_string())
}


pub fn convert_byte<T: Into<f64> + Copy + fmt::Display>(size_in_bytes: T) -> String {
    let size: f64 = size_in_bytes.into();

    if !is_int_or_float(size_in_bytes.to_string()) || size <= 0.0 {
        return "0B".to_string();
    }

    let i = (size.ln() / 1024_f64.ln()).floor() as usize;
    let p = 1024_f64.powi(i as i32);
    let mut conv = format!("{:.1}", size / p);

    // remove trailing .0 or .00
    if TRAILING_ZERO_PATTERN.is_match(&conv) {
        conv = TRAILING_ZERO_PATTERN.replace(&conv, "").to_string();
    }

    let suffixes = [
        "B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB",
    ];

    format!("{}{}", conv, suffixes[i])
}


pub fn convert_second<T: Into<f64> + Copy + fmt::Display>(seconds_input: T, verbose: bool) -> String {
    if !is_int_or_float(seconds_input.to_string()) {
        return if verbose { "0".into() } else { "0:00".into() };
    }

    let seconds = seconds_input.into();
    if seconds == 0.0 {
        return if verbose { "0".into() } else { "0:00".into() };
    }

    if seconds < 1.0 {
        return if verbose { "~0".into() } else { "~0:00".into() };
    }

    let ss = format!("{:02}", (seconds % 60.0) as u64);
    let mi = format!("{:02}", ((seconds / 60.0) % 60.0) as u64);
    let hh = format!("{:02}", ((seconds / 3600.0) % 24.0) as u64);
    let dd = format!("{:02}", ((seconds / 3600.0 / 24.0) % 30.0) as u64);
    let mo = format!("{:02}", ((seconds / 3600.0 / 24.0 / 30.0) % 12.0) as u64);
    let yy = format!("{:02}", (seconds / 3600.0 / 24.0 / 30.0 / 12.0) as u64);

    let mut result = if yy == "00" && mo == "00" && dd == "00" {
        if verbose {
            format!("{hh} hrs, {mi} mins and {ss} secs")
        } else {
            format!("{hh}:{mi}:{ss}")
        }
    } else if yy == "00" && mo == "00" {
        if verbose {
            format!("{dd} days, {hh} hrs and {mi} mins")
        } else {
            format!("{dd}:{hh}:{mi}:{ss}")
        }
    } else if yy == "00" {
        if verbose {
            format!("{mo} months, {dd} days and {hh} hrs")
        } else {
            format!("{mo}:{dd}:{hh}:{mi}:{ss}")
        }
    } else {
        if verbose {
            format!("{yy} years, {mo} months and {dd} days")
        } else {
            format!("{yy}:{mo}:{dd}:{hh}:{mi}:{ss}")
        }
    };

    if verbose {
        let subs = [
            (r"00 [a-z]+s, ", ""),
            (r"00 [a-z]+s and ", ""),
            (r"00 [a-z]+s$", ""),
            (r", ([0-9][0-9] [a-z]+s )", r" and $1"),
            (r"and 00 [a-z]+s ", ""),
            (r" and $", ""),
            (r", ([0-9][0-9] [a-z]+)$", r" and $1"),
            (r" and ([0-9][0-9] [a-z]+) and", r", $1 and"),
            (r", +$", ""),
            (r", ([0-9][0-9] [a-z]+s)$", r" and $1"),
            (r"(01 [a-z]+)s ", r"$1 "),
            (r"(01 [a-z]+)s, ", r"$1, "),
            (r"(01 [a-z]+)s$", r"$1"),
            (r", 0([0-9])", r", $1"),
            (r"and 0([0-9])", r"and $1"),
        ];

        for (pattern, replacement) in subs {
            result = Regex::new(pattern).unwrap().replace_all(&result, replacement).into_owned();
        }
    } else {
        let subs = [
            (r"^0+:0([0-9]):", r"$1:"),
            (r"^0+:([1-9])([0-9]):", r"$1$2:"),
        ];

        for (pattern, replacement) in subs {
            result = Regex::new(pattern).unwrap().replace_all(&result, replacement).into_owned();
        }
    }

    result = Regex::new(r"^0([0-9])")
        .unwrap()
        .replace(&result, r"$1")
        .into_owned();

    result
}


pub fn save_log(
    command: &str,
    host_name: &str,
    dest_file: &str,
    msg: &str,
    echo: bool,
) -> std::io::Result<()> {
    let now = chrono::Local::now();
    let ymdhms = now.format("%Y-%m-%d %H:%M:%S").to_string();

    if echo {
        println!("{} {} {} {}", host_name, command, ymdhms, msg);
    }

    let mut file = std::fs::OpenOptions::new()
        .append(true)
        .create(true)
        .open(dest_file)?;

    writeln!(file, "{} {}", ymdhms, msg)?;
    Ok(())
}


pub fn sort_dict<K, V>(dictionary: &HashMap<K, V>, based_on: &str, reverse: bool) -> IndexMap<K, V>
where
    K: Ord + Clone + std::hash::Hash + Eq,
    V: Ord + Clone,
{
    match based_on {
        "key" | "value" => {
            let mut items: Vec<(K, V)> = dictionary.iter().map(|(k, v)| (k.clone(), v.clone())).collect();

            match based_on {
                "key" => {
                    items.sort_by(|a, b| {
                        if reverse { b.0.cmp(&a.0) } else { a.0.cmp(&b.0) }
                    });
                }
                "value" => {
                    items.sort_by(|a, b| {
                        if reverse { b.1.cmp(&a.1) } else { a.1.cmp(&b.1) }
                    });
                }
                _ => unreachable!(), // We've already matched the valid cases
            }

            items.into_iter().collect()
        }

        _ => {
            // return the original unsorted dictionary
            dictionary.iter().map(|(k, v)| (k.clone(), v.clone())).collect()
        }
    }
}


pub fn to_tilda(text: &str) -> String {
    match env::var("HOME") {
        Ok(home) => text.replace(&home, "~"),
        Err(_) => text.to_string(),
    }
}
