use std::collections::HashMap;

use indexmap::IndexMap;
use mysql::{
    OptsBuilder,
    Pool,
    prelude::Queryable,
};

use crate::utils_classes::{
    MYSQLConfig,
    MYSQLValue,
};

use crate::rahavard::{
    convert_byte,
    sort_dict,
};


pub fn get_size_of_database(database_name: &str, convert: bool) -> Result<String, Box<dyn std::error::Error>> {
    let db_creds = OptsBuilder::new()
        .ip_or_hostname(Some(MYSQLConfig::MYSQL_HOST.value_string()))
        .user(Some(MYSQLConfig::MYSQL_R_USER.value_string()))
        .pass(Some(MYSQLConfig::MYSQL_R_USER_PASSWD.value_string()))
        .db_name(Some(database_name.clone()));

    let pool = Pool::new(db_creds)?;
    let mut conn = pool.get_conn()?;

    let query = "
        SELECT SUM((DATA_LENGTH + INDEX_LENGTH))
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = ?
    ";

    let result: Option<u64> = conn.exec_first(query, (database_name,))?;
    let size_in_bytes = result.unwrap_or(0);

    if convert {
        Ok(convert_byte(size_in_bytes as f64))
    } else {
        Ok(size_in_bytes.to_string())
    }
}


pub fn get_size_of_table(database_name: &str, table_name: &str, convert: bool) -> Result<String, Box<dyn std::error::Error>> {
    let db_creds = OptsBuilder::new()
        .ip_or_hostname(Some(MYSQLConfig::MYSQL_HOST.value_string()))
        .user(Some(MYSQLConfig::MYSQL_R_USER.value_string()))
        .pass(Some(MYSQLConfig::MYSQL_R_USER_PASSWD.value_string()));

    let pool = Pool::new(db_creds)?;
    let mut conn = pool.get_conn()?;

    let query = "
        SELECT SUM((DATA_LENGTH + INDEX_LENGTH))
        FROM INFORMATION_SCHEMA.TABLES
        WHERE (
            TABLE_SCHEMA = ? AND
            TABLE_NAME = ?
        )
    ";

    let result: Option<u64> = conn.exec_first(query, (database_name, table_name))?;
    let size_in_bytes = result.unwrap_or(0);

    if convert {
        Ok(convert_byte(size_in_bytes as f64))
    } else {
        Ok(size_in_bytes.to_string())
    }
}


pub fn get_tables(database_name: &str) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    let db_creds = OptsBuilder::new()
        .ip_or_hostname(Some(MYSQLConfig::MYSQL_HOST.value_string()))
        .user(Some(MYSQLConfig::MYSQL_R_USER.value_string()))
        .pass(Some(MYSQLConfig::MYSQL_R_USER_PASSWD.value_string()));

    let pool = Pool::new(db_creds)?;
    let mut conn = pool.get_conn()?;

    let query = "
        SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = ?
    ";

    let table_names: Vec<String> = conn.exec(query, (database_name,))?;

    Ok(table_names)
}


pub fn get_tables_and_sizes(
    database_name: &str,
    sort: bool,
    based_on: &str,
    reverse: bool,
    convert: bool,
) -> Result<IndexMap<String, String>, Box<dyn std::error::Error>> {
    let mut dictionary: HashMap<String, u64> = HashMap::new();

    let tables = get_tables(database_name)?;

    for table in tables {
        // passing convert as False so we can:
        //   properly sort dictionary first, and
        //   later decide whether to convert size when returning
        let size__str = get_size_of_table(database_name, &table, false)?;
        let size__int: u64 = size__str.parse().unwrap_or(0);
        dictionary.insert(table, size__int);
    }

    let final_dict = if sort {
        sort_dict(&dictionary, based_on, reverse)
    } else {
        dictionary.into_iter().collect::<IndexMap<String, u64>>()
    };

    if convert {
        let result: IndexMap<String, String> = final_dict
            .into_iter()
            .map(|(table, size)| (table, convert_byte(size as f64)))
            .collect();
        Ok(result)
    } else {
        let result: IndexMap<String, String> = final_dict
            .into_iter()
            .map(|(table, size)| (table, size.to_string()))
            .collect();
        Ok(result)
    }
}
