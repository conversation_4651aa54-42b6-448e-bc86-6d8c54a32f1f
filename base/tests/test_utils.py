from django.http import HttpRequest
from unittest import TestCase
from unittest.mock import patch, mock_open, MagicMock

from datetime import datetime, date
from io import BytesIO
from os import SEEK_END
from tempfile import TemporaryDirectory

import httpx
import json

from natsort import natsorted

from base.utils_parsers import (
    _is_invalid_ln,
    parse_ln,
)

from base.utils_classes import (
    DaemonConfig,
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    GeoLocationConfig,
    MaliciousConfig,
    MYSQLConfig,
    RouterConfig,
    RouterBoardConfig,
    SnortConfig,
    SquidConfig,
    SwitchConfig,
    UserAuditConfig,
    UserNoticeConfig,
    UserWarningConfig,
    VMwareConfig,
    VPNServerConfig,
    WindowsServerConfig,
)

from base.utils_ip import (
    get_hosts,
    is_cidr,
    is_ip_v4,
    is_ip_v6,
    is_private_v4,
    is_private_v6,
)

from base.utils_constants import (
    BINARY_PATHS,
    LAST_LINES,
    LIMITS,
    LOGICAL_OPERATORS,
    PICK_AN_OPTION,
    REFRESHES,
    SEARCH_SIGNS,
    TOPS_TO_SHOW,
)

from base.utils import (
    aggregate_values_of_dicts,
    all_values_are_0,
    break_name_of_database,
    centralize_text,
    command_instance_is_running,
    create_date_range,
    create_name_of_database,
    create_name_of_index,
    create_path_of_infile,
    create_warning_message,
    dash_to_underscore,
    dns_resolver_is_private_ip,
    end_of_command_msg,
    evenly_sized_batches,
    filter_databases,
    filter_list,
    get_date_of_source_log,
    get_directory_path_from_name_of_database,
    get_dts_and_dets_from_rts,
    get_field_headers_and_values,
    get_id_condition,
    get_name_of_function,
    get_no_of_infiles,
    get_parsed_dirs,
    get_random_wallpaper,
    get_rts_dts_dets,
    get_size_of_source_log,
    get_sum_of_values,
    get_time_condition,
    get_to_shows,
    get_today_ymd,
    highlight_searched_items_in_db_rows,
    hms_to_hourkey,
    has_non_printable_bytes,
    is_invalid_log_date,
    list_of_tuples_to_list,
    lookup_fullinfo,
    move_n_days,
    normalize_date,
    normalize_dns_question_name,
    normalize_time,
    normalize_windowsserver_category,
    paginate,
    pick_middle_item_of_list,
    quote_domain,
    read_statistics_file,
    remove_id_column,
    remove_trailing_slash,
    replace_ymdhms_at_beginning_of_line,
    reverse_date_range,
    service_is_running,
    source_log_info_line,
    strip_protocol_and_path_from_url,
    tail_file,
    trim_dict,
    trim_keys,
    underscore_to_dash,
    unquote_domain,
    verbose_time_to_millisecond,
    ymd_to_ym,
)


sensor_list_of_names_and_addresses = [
    '***********', '***********',
    'Sensor-1', 'Sensor-2'
]
sensor_dict_of_addresses_and_names = {
    '***********': 'Sensor-1',
    '***********': 'Sensor-2'
}

router_list_of_names_and_addresses = [
    'Router-1', 'Router-2',
    '***********', '***********',
]
router_dict_of_addresses_and_names = {
    '***********': 'Router-1',
    '***********': 'Router-2'
}

routerboard_list_of_names_and_addresses = [
    'RouterBoard-1', 'RouterBoard-2',
    '***********', '***********',
]
routerboard_dict_of_addresses_and_names = {
    '***********': 'RouterBoard-1',
    '***********': 'RouterBoard-2'
}

windowsserver_list_of_names_and_addresses = [
    'WindowsServer-1', 'WindowsServer-2',
    '***********', '***********',
]
windowsserver_dict_of_addresses_and_names = {
    '***********': 'WindowsServer-1',
    '***********': 'WindowsServer-2'
}

vmware_list_of_names_and_addresses = [
    'VMware-1', 'VMware-2',
    '***********', '***********',
]
vmware_dict_of_addresses_and_names = {
    '***********': 'VMware-1',
    '***********': 'VMware-2'
}

switch_list_of_names_and_addresses = [
    'Switch-1', 'Switch-2',
    '***********', '***********',
]
switch_dict_of_addresses_and_names = {
    '***********': 'Switch-1',
    '***********': 'Switch-2'
}


class TestPaginate(TestCase):
    '''
    Tests for the paginate function.

    This test suite verifies that the function correctly paginates lists and dictionaries
    based on the specified limit and page number.
    '''

    def test_paginate_list(self):
        '''
        Test paginating a list.
        '''
        data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        result = paginate(data, limit=3, page_number=2)
        self.assertEqual(result, [4, 5, 6])

    def test_paginate_dict(self):
        '''
        Test paginating a dictionary.
        '''
        data = {'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6}
        result = paginate(data, limit=2, page_number=2)
        self.assertEqual(result, {'c': 3, 'd': 4})

    def test_paginate_beyond_pages(self):
        '''
        Test requesting a page number beyond available pages.
        '''
        data = [1, 2, 3, 4, 5]
        result = paginate(data, limit=2, page_number=10)
        self.assertEqual(result, [])

        data_dict = {'a': 1, 'b': 2, 'c': 3}
        result_dict = paginate(data_dict, limit=2, page_number=10)
        self.assertEqual(result_dict, {})

    def test_paginate_first_page(self):
        '''
        Test paginating the first page.
        '''
        data = [1, 2, 3, 4, 5]
        result = paginate(data, limit=2, page_number=1)
        self.assertEqual(result, [1, 2])

class TestAllValuesAreZero(TestCase):
    '''
    Test cases for the all_values_are_0 function.

    Tests various scenarios to ensure the function correctly identifies
    dictionaries where all values are zero.
    '''

    def test_all_zeros(self):
        '''
        Test when all dictionary values are zero.
        '''
        self.assertTrue(all_values_are_0({'a': 0, 'b': 0, 'c': 0}))

    def test_some_non_zeros(self):
        '''
        Test when some dictionary values are non-zero.
        '''
        self.assertFalse(all_values_are_0({'a': 0, 'b': 1, 'c': 0}))
        self.assertFalse(all_values_are_0({'a': 0, 'b': -1, 'c': 0}))

    def test_empty_dict(self):
        '''
        Test with an empty dictionary.
        '''
        self.assertTrue(all_values_are_0({}))

class TestTrimKeys(TestCase):
    '''
    Test cases for the trim_keys function.
    '''

    def test_trim_keys_with_short_keys(self):
        '''
        Test that keys shorter than max_length remain unchanged.
        '''
        input_dict = {'a': 1, 'bc': 2, 'def': 3}
        result = trim_keys(input_dict, 5)
        self.assertEqual(result, input_dict)

    def test_trim_keys_with_long_keys(self):
        '''
        Test that keys longer than max_length are trimmed and appended with '...'.
        '''
        input_dict = {'short': 1, 'very_long_key': 2, 'another_long_key': 3}
        expected = {'short': 1, 'very_...': 2, 'anoth...': 3}
        result = trim_keys(input_dict, 5)
        self.assertEqual(result, expected)

    def test_trim_keys_with_exact_length(self):
        '''
        Test that keys exactly at max_length remain unchanged.
        '''
        input_dict = {'abcde': 1, 'fghij': 2, 'klmno': 3}
        result = trim_keys(input_dict, 5)
        self.assertEqual(result, input_dict)

    def test_trim_keys_with_empty_dict(self):
        '''
        Test that an empty dictionary returns an empty dictionary.
        '''
        input_dict = {}
        result = trim_keys(input_dict, 10)
        self.assertEqual(result, {})

    def test_trim_keys_with_mixed_lengths(self):
        '''
        Test with a mix of short, exact, and long keys.
        '''
        input_dict = {'a': 1, 'abcde': 2, 'abcdefghij': 3}
        expected = {'a': 1, 'abcde': 2, 'abcde...': 3}
        result = trim_keys(input_dict, 5)
        self.assertEqual(result, expected)

class TestMoveNDays(TestCase):
    '''
    Tests for the move_n_days function.

    This test suite verifies that the function correctly moves a date forward
    or backward by a specified number of days.
    '''

    def test_move_forward(self):
        '''
        Test moving a date forward by a positive number of days.
        '''
        result = move_n_days('2023-01-01', 5)
        self.assertEqual(result, '2023-01-06')

    def test_move_backward(self):
        '''
        Test moving a date backward by a negative number of days.
        '''
        result = move_n_days('2023-01-01', -5)
        self.assertEqual(result, '2022-12-27')

    def test_month_boundary(self):
        '''
        Test moving a date across a month boundary.
        '''
        result = move_n_days('2023-01-31', 1)
        self.assertEqual(result, '2023-02-01')

    def test_year_boundary(self):
        '''
        Test moving a date across a year boundary.
        '''
        result = move_n_days('2023-12-31', 1)
        self.assertEqual(result, '2024-01-01')

    def test_leap_year(self):
        '''
        Test moving a date around February 29th in a leap year.
        '''
        result = move_n_days('2024-02-28', 1)
        self.assertEqual(result, '2024-02-29')

        result = move_n_days('2024-02-29', 1)
        self.assertEqual(result, '2024-03-01')

class TestGetTodayYmd(TestCase):
    '''
    Tests for the get_today_ymd function.

    This test suite verifies that the function correctly returns the current date
    in ISO 8601 format (YYYY-MM-DD).
    '''

    @patch('base.utils.date')
    def test_get_today_ymd(self, mock_date):
        '''
        Test that get_today_ymd returns the current date in ISO 8601 format.
        '''
        # Setup mock date
        mock_date.today.return_value = date(2023, 5, 15)

        # Call the function
        result = get_today_ymd()

        # Verify the result
        self.assertEqual(result, '2023-05-15')
        mock_date.today.assert_called_once()

    def test_get_today_ymd_format(self):
        '''
        Test that get_today_ymd returns a string in the correct format.
        '''
        result = get_today_ymd()

        # Check that result is a string
        self.assertIsInstance(result, str)

        # Check that result matches YYYY-MM-DD format
        self.assertRegex(result, r'^\d{4}-\d{2}-\d{2}$')

class TestReadStatisticsFile(TestCase):
    '''
    Test cases for the read_statistics_file function.
    '''

    @patch('base.utils.settings')
    @patch('builtins.open', new_callable=mock_open, read_data='{"key": "value"}')
    def test_read_logs_statistics_file(self, mock_file, mock_settings):
        '''
        Test reading logs statistics file successfully.
        '''
        mock_settings.LOGS_STATISTICS_FILE = '/path/to/logs_stats.json'

        result = read_statistics_file('logs')

        mock_file.assert_called_once_with('/path/to/logs_stats.json')
        self.assertEqual(result, {'key': 'value'})

    @patch('base.utils.settings')
    @patch('builtins.open', new_callable=mock_open, read_data='{"key": "value"}')
    def test_read_databases_statistics_file(self, mock_file, mock_settings):
        '''
        Test reading databases statistics file successfully.
        '''
        mock_settings.DATABASES_STATISTICS_FILE = '/path/to/db_stats.json'

        result = read_statistics_file('databases')

        mock_file.assert_called_once_with('/path/to/db_stats.json')
        self.assertEqual(result, {'key': 'value'})

    @patch('base.utils.settings')
    @patch('builtins.open', side_effect=Exception('File error'))
    def test_read_statistics_file_with_error(self, mock_file, mock_settings):
        '''
        Test handling of errors when reading statistics file.
        '''
        mock_settings.PARSED_DATES_STATISTICS_FILE = '/path/to/dates_stats.json'

        result = read_statistics_file('parsed-dates')

        self.assertEqual(result, {})

    def test_read_statistics_file_with_invalid_mode(self):
        '''
        Test that an empty dictionary is returned for invalid modes.
        '''
        result = read_statistics_file('invalid-mode')

        self.assertEqual(result, {})

class TestAggregateValuesOfDicts(TestCase):
    '''
    Test cases for the aggregate_values_of_dicts function.
    '''

    def test_basic_aggregation(self):
        '''
        Test basic aggregation of dictionaries with some common keys.
        '''
        list_of_dicts = [{'a': 1, 'b': 2}, {'a': 3, 'c': 4}]
        result = aggregate_values_of_dicts(list_of_dicts)
        self.assertEqual(result, {'a': 4, 'c': 4, 'b': 2})

    def test_empty_list(self):
        '''
        Test with an empty list of dictionaries.
        '''
        result = aggregate_values_of_dicts([])
        self.assertEqual(result, {})

    def test_single_dict(self):
        '''
        Test with a single dictionary.
        '''
        result = aggregate_values_of_dicts([{'x': 10, 'y': 20}])
        self.assertEqual(result, {'x': 10, 'y': 20})

    def test_multiple_dicts_no_overlap(self):
        '''
        Test with multiple dictionaries with no overlapping keys.
        '''
        list_of_dicts = [{'a': 1}, {'b': 2}, {'c': 3}]
        result = aggregate_values_of_dicts(list_of_dicts)
        self.assertEqual(result, {'c': 3, 'b': 2, 'a': 1})

    def test_sorting_by_value(self):
        '''
        Test that results are sorted by value in descending order.
        '''
        list_of_dicts = [{'a': 1, 'b': 5}, {'c': 10, 'd': 2}]
        result = aggregate_values_of_dicts(list_of_dicts)
        expected_order = ['c', 'b', 'd', 'a']
        self.assertEqual(list(result.keys()), expected_order)

class TestHighlightSearchedItemsInDbRows(TestCase):
    '''
    Tests for the highlight_searched_items_in_db_rows function.

    This test suite verifies that the function correctly highlights searched items
    in database rows based on the provided search criteria.
    '''

    def test_empty_inputs(self):
        '''
        Test that the function returns the original db_rows when inputs are empty.
        '''
        # Empty db_rows
        result = highlight_searched_items_in_db_rows(
            db_rows=[],
            field_headers_and_values={'header': ['value']},
            match_case=True,
            db_headers_with_indexes={'header': 0},
            multi_day_report_allowed=False
        )
        self.assertEqual(result, [])

        # Empty field_headers_and_values
        db_rows = [['item1', 'item2']]
        result = highlight_searched_items_in_db_rows(
            db_rows=db_rows,
            field_headers_and_values={},
            match_case=True,
            db_headers_with_indexes={'header': 0},
            multi_day_report_allowed=False
        )
        self.assertEqual(result, db_rows)

    def test_case_sensitive_highlighting(self):
        '''
        Test that the function correctly highlights items with case sensitivity.
        '''
        db_rows = [['User123', 'Login', '***********']]
        field_headers_and_values = {'action': ['Login']}
        db_headers_with_indexes = {'action': 1}

        result = highlight_searched_items_in_db_rows(
            db_rows=db_rows,
            field_headers_and_values=field_headers_and_values,
            match_case=True,
            db_headers_with_indexes=db_headers_with_indexes,
            multi_day_report_allowed=False
        )

        # The 'Login' should be highlighted
        self.assertIn('<mark>Login</mark>', str(result[0][1]))

        # Test with non-matching case
        field_headers_and_values = {'action': ['login']}
        result = highlight_searched_items_in_db_rows(
            db_rows=db_rows,
            field_headers_and_values=field_headers_and_values,
            match_case=True,
            db_headers_with_indexes=db_headers_with_indexes,
            multi_day_report_allowed=False
        )

        # No highlighting should occur with case-sensitive search
        self.assertNotIn('<mark>', str(result[0][1]))

    def test_case_insensitive_highlighting(self):
        '''
        Test that the function correctly highlights items without case sensitivity.
        '''
        db_rows = [['User123', 'Login', '***********']]
        field_headers_and_values = {'action': ['login']}
        db_headers_with_indexes = {'action': 1}

        result = highlight_searched_items_in_db_rows(
            db_rows=db_rows,
            field_headers_and_values=field_headers_and_values,
            match_case=False,
            db_headers_with_indexes=db_headers_with_indexes,
            multi_day_report_allowed=False
        )

        # The 'Login' should be highlighted even though search was 'login'
        self.assertIn('<mark>Login</mark>', str(result[0][1]))

    def test_multi_day_report_adjustment(self):
        '''
        Test that the function correctly adjusts indexes when multi_day_report_allowed is True.
        '''
        db_rows = [['User123', 'Login', '***********']]
        field_headers_and_values = {'action': ['Login']}
        db_headers_with_indexes = {'action': 2}  # Index is 2, but will be adjusted to 1

        result = highlight_searched_items_in_db_rows(
            db_rows=db_rows,
            field_headers_and_values=field_headers_and_values,
            match_case=True,
            db_headers_with_indexes=db_headers_with_indexes,
            multi_day_report_allowed=True
        )

        # The 'Login' should be highlighted after index adjustment
        self.assertIn('<mark>Login</mark>', str(result[0][1]))

    def test_multiple_search_values(self):
        '''
        Test that the function correctly highlights multiple search values for the same field.
        '''
        db_rows = [['User123', 'Login', '***********'], ['Admin', 'Logout', '********']]
        field_headers_and_values = {'action': ['Login', 'Logout']}
        db_headers_with_indexes = {'action': 1}

        result = highlight_searched_items_in_db_rows(
            db_rows=db_rows,
            field_headers_and_values=field_headers_and_values,
            match_case=True,
            db_headers_with_indexes=db_headers_with_indexes,
            multi_day_report_allowed=False
        )

        # Both 'Login' and 'Logout' should be highlighted
        self.assertIn('<mark>Login</mark>', str(result[0][1]))
        self.assertIn('<mark>Logout</mark>', str(result[1][1]))

    def test_multiple_fields_highlighting(self):
        '''
        Test that the function correctly highlights items across multiple fields.
        '''
        db_rows = [['User123', 'Login', '***********']]
        field_headers_and_values = {
            'username': ['User'],
            'action': ['Login'],
            'ip': ['192.168']
        }
        db_headers_with_indexes = {'username': 0, 'action': 1, 'ip': 2}

        result = highlight_searched_items_in_db_rows(
            db_rows=db_rows,
            field_headers_and_values=field_headers_and_values,
            match_case=True,
            db_headers_with_indexes=db_headers_with_indexes,
            multi_day_report_allowed=False
        )

        # All matching parts should be highlighted
        self.assertIn('<mark>User</mark>', str(result[0][0]))
        self.assertIn('<mark>Login</mark>', str(result[0][1]))
        self.assertIn('<mark>192.168</mark>', str(result[0][2]))

class TestQuoteDomain(TestCase):
    '''
    Tests for the quote_domain function.

    This test suite verifies that the function correctly percent-encodes
    special characters in domain names.
    '''

    def test_simple_domain(self):
        '''
        Test that a simple domain without special characters remains unchanged.
        '''
        result = quote_domain('example.com')
        self.assertEqual(result, 'example.com')

    def test_domain_with_slash(self):
        '''
        Test that a domain with a slash character gets properly encoded.
        '''
        result = quote_domain('example.com/test')
        self.assertEqual(result, 'example.com%2Ftest')

    def test_domain_with_query_params(self):
        '''
        Test that a domain with query parameters gets properly encoded.
        '''
        result = quote_domain('example.com?query=1')
        self.assertEqual(result, 'example.com%3Fquery%3D1')

    def test_domain_with_special_chars(self):
        '''
        Test that a domain with various special characters gets properly encoded.
        '''
        result = quote_domain('sub.example.com:8080/path?q=test&p=2')
        self.assertEqual(result, 'sub.example.com%3A8080%2Fpath%3Fq%3Dtest%26p%3D2')

    def test_empty_string(self):
        '''
        Test that an empty string returns an empty string.
        '''
        result = quote_domain('')
        self.assertEqual(result, '')

class TestUnquoteDomain(TestCase):
    '''
    Tests for the unquote_domain function.

    This test suite verifies that the function correctly decodes
    percent-encoded characters in domain names.
    '''

    def test_simple_domain(self):
        '''
        Test that a simple domain without encoded characters remains unchanged.
        '''
        result = unquote_domain('example.com')
        self.assertEqual(result, 'example.com')

    def test_encoded_dots(self):
        '''
        Test that a domain with encoded dots gets properly decoded.
        '''
        result = unquote_domain('example%2Ecom')
        self.assertEqual(result, 'example.com')

    def test_encoded_special_chars(self):
        '''
        Test that a domain with various encoded special characters gets properly decoded.
        '''
        result = unquote_domain('sub%2Eexample%2Ecom%3A8080%2Fpath%3Fq%3Dtest%26p%3D2')
        self.assertEqual(result, 'sub.example.com:8080/path?q=test&p=2')

    def test_empty_string(self):
        '''
        Test that an empty string returns an empty string.
        '''
        result = unquote_domain('')
        self.assertEqual(result, '')

class TestCreateDateRange(TestCase):
    '''
    Tests for the create_date_range function.

    This test suite verifies that the function correctly generates a list of dates
    between the given start and end dates.
    '''

    def test_normal_date_range(self):
        '''
        Test that a normal date range returns all dates between start and end, inclusive.
        '''
        result = create_date_range('2023-01-01', '2023-01-05')
        expected = ['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05']
        self.assertEqual(result, expected)

    def test_single_day_range(self):
        '''
        Test that when start and end dates are the same, a single date is returned.
        '''
        result = create_date_range('2023-01-01', '2023-01-01')
        self.assertEqual(result, ['2023-01-01'])

    def test_only_start_date(self):
        '''
        Test that when only start_date is provided, it returns a list with just that date.
        '''
        result = create_date_range('2023-01-01')
        self.assertEqual(result, ['2023-01-01'])

    def test_only_end_date(self):
        '''
        Test that when only end_date is provided, it returns a list with just that date.
        '''
        result = create_date_range(end_date='2023-01-01')
        self.assertEqual(result, ['2023-01-01'])

    def test_no_dates(self):
        '''
        Test that when neither start_date nor end_date is provided, an empty list is returned.
        '''
        result = create_date_range()
        self.assertEqual(result, [])

    def test_invalid_date_format(self):
        '''
        Test that when dates are in invalid format, an empty list is returned.
        '''
        result = create_date_range('01/01/2023', '05/01/2023')
        self.assertEqual(result, [])

    def test_invalid_date(self):
        '''
        Test that when a date is invalid (e.g., February 30), an empty list is returned.
        '''
        result = create_date_range('2023-02-28', '2023-02-30')
        self.assertEqual(result, [])

    def test_end_date_before_start_date(self):
        '''
        Test behavior when end_date is before start_date.
        '''
        result = create_date_range('2023-01-05', '2023-01-01')
        self.assertEqual(result, [])

class TestPickMiddleItemOfList(TestCase):
    '''
    Tests for the pick_middle_item_of_list function.

    This test suite verifies that the function correctly returns the middle item
    from a list, or None if the list is empty.
    '''

    def test_empty_list(self):
        '''
        Test that an empty list returns None.
        '''
        result = pick_middle_item_of_list([])
        self.assertIsNone(result)

    def test_odd_length_list(self):
        '''
        Test that a list with odd length returns the middle item.
        '''
        result = pick_middle_item_of_list([1, 2, 3, 4, 5])
        self.assertEqual(result, 3)

    def test_even_length_list(self):
        '''
        Test that a list with even length returns the lower middle item.
        '''
        result = pick_middle_item_of_list([1, 2, 3, 4])
        self.assertEqual(result, 2)

    def test_single_item_list(self):
        '''
        Test that a list with a single item returns that item.
        '''
        result = pick_middle_item_of_list([42])
        self.assertEqual(result, 42)

    def test_with_different_types(self):
        '''
        Test that the function works with lists containing different types.
        '''
        result = pick_middle_item_of_list(['a', 2, True, None, 'z'])
        self.assertEqual(result, True)

class TestGetFieldHeadersAndValues(TestCase):
    '''
    Tests for the get_field_headers_and_values function.

    This test suite verifies that the function correctly extracts and processes
    field headers and their corresponding values from a Django request.
    '''

    @patch('base.utils.get_to_shows')
    def test_single_value_per_header(self, mock_get_to_shows):
        '''
        Test with single values for each header.
        '''
        # Setup
        mock_request = MagicMock()
        db_headers = ['username', 'action', 'ip']

        # Configure mock to return different values for different headers
        mock_get_to_shows.side_effect = lambda req, header: {
            'username': 'admin',
            'action': 'login',
            'ip': '***********'
        }.get(header)

        # Call the function
        result = get_field_headers_and_values(mock_request, db_headers)

        # Verify the result
        expected = {
            'username': ['admin'],
            'action': ['login'],
            'ip': ['***********']
        }
        self.assertEqual(result, expected)

        # Verify get_to_shows was called correctly
        self.assertEqual(mock_get_to_shows.call_count, 3)

    @patch('base.utils.get_to_shows')
    def test_multiple_values_per_header(self, mock_get_to_shows):
        '''
        Test with multiple values separated by SEARCH_SIGNS.field_separator for some headers.
        '''
        # Setup
        mock_request = MagicMock()
        db_headers = ['username', 'action', 'ip']

        # Configure mock to return different values for different headers
        mock_get_to_shows.side_effect = lambda req, header: {
            'username': f'admin{SEARCH_SIGNS.field_separator}user',
            'action': f'login{SEARCH_SIGNS.field_separator}logout',
            'ip': '***********'
        }.get(header)

        # Call the function
        result = get_field_headers_and_values(mock_request, db_headers)

        # Verify the result
        expected = {
            'username': ['admin', 'user'],
            'action': ['login', 'logout'],
            'ip': ['***********']
        }
        self.assertEqual(result, expected)

    @patch('base.utils.get_to_shows')
    def test_with_empty_values(self, mock_get_to_shows):
        '''
        Test with some headers having empty values.
        '''
        # Setup
        mock_request = MagicMock()
        db_headers = ['username', 'action', 'ip']

        # Configure mock to return different values for different headers
        mock_get_to_shows.side_effect = lambda req, header: {
            'username': 'admin',
            'action': '',  # Empty value
            'ip': None     # None value
        }.get(header)

        # Call the function
        result = get_field_headers_and_values(mock_request, db_headers)

        # Verify the result - only non-empty values should be included
        expected = {
            'username': ['admin']
        }
        self.assertEqual(result, expected)

    @patch('base.utils.get_to_shows')
    def test_with_whitespace(self, mock_get_to_shows):
        '''
        Test that whitespace is properly stripped from values.
        '''
        # Setup
        mock_request = MagicMock()
        db_headers = ['username']

        # Configure mock to return value with whitespace
        mock_get_to_shows.return_value = f'  admin  {SEARCH_SIGNS.field_separator}  user  '

        # Call the function
        result = get_field_headers_and_values(mock_request, db_headers)

        # Verify the result - whitespace should be stripped
        expected = {
            'username': ['admin', 'user']
        }
        self.assertEqual(result, expected)

class TestGetToShows(TestCase):
    '''
    Tests for the get_to_shows function.

    This test suite verifies that the function correctly extracts and processes
    parameters from a Django request object based on the provided arguments.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.get_request = MagicMock()
        self.get_request.method = 'GET'
        self.get_request.GET = {}
        self.get_request.COOKIES = {}

        self.post_request = MagicMock()
        self.post_request.method = 'POST'
        self.post_request.POST = {}
        self.post_request.COOKIES = {}

    def test_get_single_parameter(self):
        '''
        Test extracting a single parameter from a GET request.
        '''
        self.get_request.GET = {'page': '2'}
        result = get_to_shows(self.get_request, 'page')
        self.assertEqual(result, 2)

    def test_get_multiple_parameters(self):
        '''
        Test extracting multiple parameters from a GET request.
        '''
        self.get_request.GET = {'page': '2', 'limit': '10'}
        result = get_to_shows(self.get_request, 'page', 'limit')
        self.assertEqual(result, [2, 10])

    def test_post_parameters(self):
        '''
        Test extracting parameters from a POST request.
        '''
        self.post_request.POST = {'refresh': '10', 'top': '5'}
        result = get_to_shows(self.post_request, 'refresh', 'top')
        self.assertEqual(result, [10, 5])

    def test_latest_id_from_cookie(self):
        '''
        Test extracting latest-id from cookies.
        '''
        self.get_request.COOKIES = {'latest-id': '42'}
        result = get_to_shows(self.get_request, 'latest-id')
        self.assertEqual(result, 42)

    def test_boolean_parameters(self):
        '''
        Test extracting boolean parameters.
        '''
        self.get_request.GET = {
            'from-dropdown': 'true',
            'match-case': 'true',
            'overview': 'false'
        }
        result = get_to_shows(self.get_request, 'from-dropdown', 'match-case', 'overview')
        self.assertEqual(result, [True, True, False])

    def test_logical_operator_parameter(self):
        '''
        Test extracting logical-operator parameter with validation.
        '''
        # Valid value
        self.get_request.GET = {'logical-operator': 'and'}
        result = get_to_shows(self.get_request, 'logical-operator')
        self.assertEqual(result, 'AND')

        # Invalid value should return default
        self.get_request.GET = {'logical-operator': 'invalid'}
        result = get_to_shows(self.get_request, 'logical-operator')
        self.assertEqual(result, LOGICAL_OPERATORS.default)

    def test_time_parameters(self):
        '''
        Test extracting time parameters.
        '''
        self.get_request.GET = {'time': '10:30:45', 'time-end': '15:45:00'}
        result = get_to_shows(self.get_request, 'time', 'time-end')
        self.assertEqual(result, ['10:30', '15:45'])

    def test_recent_parameter(self):
        '''
        Test extracting recent parameter.
        '''
        self.get_request.GET = {'recent': 'Week'}
        result = get_to_shows(self.get_request, 'recent')
        self.assertEqual(result, 'Week')

        # PICK_AN_OPTION should return None
        self.get_request.GET = {'recent': PICK_AN_OPTION}
        result = get_to_shows(self.get_request, 'recent')
        self.assertIsNone(result)

    def test_default_values(self):
        '''
        Test default values when parameters are not provided.
        '''
        result = get_to_shows(self.get_request, 'page', 'limit', 'refresh', 'top', 'last-line')
        self.assertEqual(result, [1, LIMITS.default, REFRESHES.default, TOPS_TO_SHOW.default, LAST_LINES.default])

    def test_limit_pick_options(self):
        '''
        Test the pick_lowest, pick_middle, and pick_highest options for limit parameter.
        '''
        # Test pick_lowest
        result = get_to_shows(self.get_request, 'limit', pick_lowest=True)
        self.assertEqual(result, LIMITS.values[0])

        # Test pick_middle
        result = get_to_shows(self.get_request, 'limit', pick_middle=True)
        self.assertEqual(result, pick_middle_item_of_list(LIMITS.values))

        # Test pick_highest
        result = get_to_shows(self.get_request, 'limit', pick_highest=True)
        self.assertEqual(result, LIMITS.values[-1])

    def test_chosen_sensor_name_from_cookie(self):
        '''
        Test extracting chosen-sensor-name from cookies.
        '''
        self.get_request.COOKIES = {'chosensensorname': 'sensor1'}
        result = get_to_shows(self.get_request, 'chosen-sensor-name')
        self.assertEqual(result, 'sensor1')

        # Test with 'None' string in cookie
        self.get_request.COOKIES = {'chosensensorname': 'None'}
        result = get_to_shows(self.get_request, 'chosen-sensor-name')
        self.assertIsNone(result)

    def test_unsupported_method(self):
        '''
        Test with an unsupported HTTP method.
        '''
        request = MagicMock()
        request.method = 'PUT'
        result = get_to_shows(request, 'page')
        self.assertIsNone(result)

class TestGetDtsAndDetsFromRts(TestCase):
    '''
    Tests for the get_dts_and_dets_from_rts function.

    This test suite verifies that the function correctly calculates start and end dates
    based on the provided range context (rcnt).
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        now = datetime.now()
        self.y = now.year  ## 2023

    def test_month_range(self):
        '''
        Test that the function correctly calculates date ranges for months.
        '''
        # Test January (31 days)
        result = get_dts_and_dets_from_rts('January')
        self.assertEqual(result, (f'{self.y}-01-01', f'{self.y}-01-31'))

        # Test February (28 days in non-leap year)
        result = get_dts_and_dets_from_rts('February')
        self.assertEqual(result, (f'{self.y}-02-01', f'{self.y}-02-28'))

        # Test April (30 days)
        result = get_dts_and_dets_from_rts('April')
        self.assertEqual(result, (f'{self.y}-04-01', f'{self.y}-04-30'))

    def test_season_range(self):
        '''
        Test that the function correctly calculates date ranges for seasons.
        '''
        # Test Spring (April-June)
        result = get_dts_and_dets_from_rts('Spring')
        self.assertEqual(result, (f'{self.y}-04-01', f'{self.y}-06-30'))

        # Test Summer (July-September)
        result = get_dts_and_dets_from_rts('Summer')
        self.assertEqual(result, (f'{self.y}-07-01', f'{self.y}-09-30'))

        # Test Fall (October-December)
        result = get_dts_and_dets_from_rts('Fall')
        self.assertEqual(result, (f'{self.y}-10-01', f'{self.y}-12-31'))

        # Test Winter (January-March)
        result = get_dts_and_dets_from_rts('Winter')
        self.assertEqual(result, (f'{self.y}-01-01', f'{self.y}-03-31'))

    def test_relative_time_range(self):
        '''
        Test that the function correctly calculates date ranges for relative time periods.
        '''
        today_ymd = get_today_ymd()

        ## NOTE numbers here passed to move_n_days are borrowed
        ##      from delta_mapping dictionary in base/utils.py

        # Test Week (7 days back)
        result = get_dts_and_dets_from_rts('Week')
        self.assertEqual(result, (move_n_days(today_ymd, -7), today_ymd))

        # Test 2 Weeks (14 days back)
        result = get_dts_and_dets_from_rts('2 Weeks')
        self.assertEqual(result, (move_n_days(today_ymd, -14), today_ymd))

        # Test Month (30 days back)
        result = get_dts_and_dets_from_rts('Month')
        self.assertEqual(result, (move_n_days(today_ymd, -30), today_ymd))

        # Test Year (365 days back)
        result = get_dts_and_dets_from_rts('Year')
        self.assertEqual(result, (move_n_days(today_ymd, -365), today_ymd))

        # Test Year (730 days back)
        result = get_dts_and_dets_from_rts('2 Years')
        self.assertEqual(result, (move_n_days(today_ymd, -730), today_ymd))

    def test_invalid_range(self):
        '''
        Test that the function returns (None, None) for invalid range contexts.
        '''
        result = get_dts_and_dets_from_rts('Invalid Range')
        self.assertEqual(result, (None, None))

        result = get_dts_and_dets_from_rts('')
        self.assertEqual(result, (None, None))

class TestGetRtsDtsDets(TestCase):
    '''
    Tests for the get_rts_dts_dets function.

    This test suite verifies that the function correctly determines the appropriate
    recent_to_show, date_to_show, and date_end_to_show values based on the provided inputs.
    '''

    @patch('base.utils.get_dts_and_dets_from_rts')
    def test_with_recent_to_show(self, mock_get_dates):
        '''
        Test that when recent_to_show is provided, it calls get_dts_and_dets_from_rts.
        '''
        mock_get_dates.return_value = ('2023-10-01', '2023-10-07')

        result = get_rts_dts_dets('Week', None, None)

        self.assertEqual(result, ('Week', '2023-10-01', '2023-10-07'))
        mock_get_dates.assert_called_once_with(rcnt='Week')

    def test_with_valid_date_range(self):
        '''
        Test with valid start and end dates.
        '''
        result = get_rts_dts_dets(None, '2023-10-01', '2023-10-07')

        self.assertEqual(result, (None, '2023-10-01', '2023-10-07'))

    def test_with_invalid_date_range(self):
        '''
        Test with end date preceding start date.
        '''
        result = get_rts_dts_dets(None, '2023-10-07', '2023-10-01')

        self.assertEqual(result, (None, '2023-10-07', None))

    def test_with_only_start_date(self):
        '''
        Test with only start date provided.
        '''
        result = get_rts_dts_dets(None, '2023-10-01', None)

        self.assertEqual(result, (None, '2023-10-01', None))

    def test_with_only_end_date(self):
        '''
        Test with only end date provided.
        '''
        result = get_rts_dts_dets(None, None, '2023-10-07')

        self.assertEqual(result, (None, '2023-10-07', None))

    @patch('base.utils.get_dts_and_dets_from_rts')
    @patch('base.utils.RECENTS_TO_SHOW.default', '2 Weeks')
    def test_with_no_parameters(self, mock_get_dates):
        '''
        Test with no parameters provided.
        '''
        mock_get_dates.return_value = ('2023-10-01', '2023-10-15')

        result = get_rts_dts_dets(None, None, None)

        self.assertEqual(result, ('2 Weeks', '2023-10-01', '2023-10-15'))
        mock_get_dates.assert_called_once_with(rcnt='2 Weeks')

class TestGetTimeCondition(TestCase):
    '''
    Tests for the get_time_condition function.

    This test suite verifies that the function correctly generates SQL time conditions
    based on the provided time range parameters.
    '''

    def test_with_both_times_and_search_string(self):
        '''
        Test with both start and end times provided along with a search string.
        '''
        result = get_time_condition('search_string', '02:00', '04:00')
        expected_condition = 'AND (Time BETWEEN %s AND %s)'
        expected_params = ['02:00:00', '04:00:59']
        self.assertEqual(result, (expected_condition, expected_params))

    def test_with_both_times_no_search_string(self):
        '''
        Test with both start and end times provided but no search string.
        '''
        result = get_time_condition('', '02:00', '04:00')
        expected_condition = 'WHERE (Time BETWEEN %s AND %s)'
        expected_params = ['02:00:00', '04:00:59']
        self.assertEqual(result, (expected_condition, expected_params))

    def test_with_only_start_time(self):
        '''
        Test with only start time provided.
        '''
        result = get_time_condition('search_string', '02:00', '')
        expected_condition = 'AND (Time BETWEEN %s AND %s)'
        expected_params = ['02:00:00', '02:00:59']
        self.assertEqual(result, (expected_condition, expected_params))

    def test_with_only_end_time(self):
        '''
        Test with only end time provided.
        '''
        result = get_time_condition('search_string', '', '04:00')
        expected_condition = 'AND (Time BETWEEN %s AND %s)'
        expected_params = ['00:00:00', '04:00:59']
        self.assertEqual(result, (expected_condition, expected_params))

    def test_with_no_times(self):
        '''
        Test with neither start nor end time provided.
        '''
        result = get_time_condition('search_string', '', '')
        expected_condition = ''
        expected_params = []
        self.assertEqual(result, (expected_condition, expected_params))

    def test_with_no_times_no_search_string(self):
        '''
        Test with neither times nor search string provided.
        '''
        result = get_time_condition('', '', '')
        expected_condition = ''
        expected_params = []
        self.assertEqual(result, (expected_condition, expected_params))

class TestGetIdCondition(TestCase):
    '''
    Tests for the get_id_condition function.

    This test suite verifies that the function correctly generates SQL ID conditions
    based on the provided parameters.
    '''

    def test_empty_latest_id(self):
        '''
        Test that an empty latest_id returns an empty string.
        '''
        result = get_id_condition('search_string', None)
        self.assertEqual(result, '')

        result = get_id_condition('search_string', 0)
        self.assertEqual(result, '')

    def test_with_search_string_default_params(self):
        '''
        Test with search string and default parameters.
        '''
        result = get_id_condition('search_string', 100)
        self.assertEqual(result, 'AND (ID > 100)')

    def test_without_search_string_default_params(self):
        '''
        Test without search string and default parameters.
        '''
        result = get_id_condition('', 100)
        self.assertEqual(result, 'WHERE (ID > 100)')

    def test_newest_on_top_no_refresh(self):
        '''
        Test with newest_on_top=True and refresh_to_show=False.
        '''
        result = get_id_condition('search_string', 100, newest_on_top=True, refresh_to_show=False)
        self.assertEqual(result, 'AND (ID < 100)')

        result = get_id_condition('', 100, newest_on_top=True, refresh_to_show=False)
        self.assertEqual(result, 'WHERE (ID < 100)')

    def test_newest_on_top_with_refresh(self):
        '''
        Test with newest_on_top=True and refresh_to_show=True.
        '''
        result = get_id_condition('search_string', 100, newest_on_top=True, refresh_to_show=True)
        self.assertEqual(result, 'AND (ID > 100)')

        result = get_id_condition('', 100, newest_on_top=True, refresh_to_show=True)
        self.assertEqual(result, 'WHERE (ID > 100)')

    def test_oldest_on_top_with_refresh(self):
        '''
        Test with newest_on_top=False and refresh_to_show=True.
        '''
        result = get_id_condition('search_string', 100, newest_on_top=False, refresh_to_show=True)
        self.assertEqual(result, 'AND (ID > 100)')

        result = get_id_condition('', 100, newest_on_top=False, refresh_to_show=True)
        self.assertEqual(result, 'WHERE (ID > 100)')

class TestLookupFullinfo(TestCase):
    '''
    Tests for the lookup_fullinfo function.

    This test suite verifies that the function correctly retrieves and processes
    information about an IP address or domain.
    '''

    @patch('base.utils.httpx.Client')
    def test_successful_lookup(self, mock_client):
        '''
        Test that a successful API response is properly processed.
        '''
        # Setup mock response
        mock_response = MagicMock()
        mock_response.text = json.dumps({
            'ipVersion': 4,
            'ipAddress': '*******',
            'latitude': 37.386051,
            'longitude': -122.083847,
            'countryName': 'United States of America',
            'countryCode': 'US',
            'timeZone': '-08:00',
            'zipCode': '94035',
            'cityName': 'Mountain View',
            'regionName': 'California',
            'isProxy': False,
            'continent': 'Americas',
            'continentCode': 'AM'
        })
        mock_response.raise_for_status.return_value = None

        # Setup mock client
        mock_client_instance = MagicMock()
        mock_client_instance.get.return_value = mock_response
        mock_client.return_value.__enter__.return_value = mock_client_instance

        # Call the function
        result = lookup_fullinfo('*******', 5.0)

        # Verify the result
        self.assertEqual(result['ipAddress'], '*******')
        self.assertEqual(result['countryName'], 'United States of America')
        self.assertEqual(result['cityName'], 'Mountain View')
        self.assertFalse(result['isProxy'])

        # Verify the mock was called correctly
        mock_client_instance.get.assert_called_once()

    @patch('base.utils.httpx.Client')
    def test_http_status_error(self, mock_client):
        '''
        Test that an HTTP status error is handled properly.
        '''
        # Setup mock to raise HTTPStatusError
        mock_client_instance = MagicMock()
        mock_client_instance.get.side_effect = httpx.HTTPStatusError(
            'Error', request=MagicMock(), response=MagicMock()
        )
        mock_client.return_value.__enter__.return_value = mock_client_instance

        # Call the function
        result = lookup_fullinfo('invalid-domain', 5.0)

        # Verify the result is an empty dict
        self.assertEqual(result, {})

    @patch('base.utils.httpx.Client')
    def test_general_exception(self, mock_client):
        '''
        Test that a general exception is handled properly.
        '''
        # Setup mock to raise a general exception
        mock_client_instance = MagicMock()
        mock_client_instance.get.side_effect = Exception('Connection error')
        mock_client.return_value.__enter__.return_value = mock_client_instance

        # Call the function
        result = lookup_fullinfo('*******', 5.0)

        # Verify the result is an empty dict
        self.assertEqual(result, {})

    @patch('base.utils.httpx.Client')
    def test_domain_lookup(self, mock_client):
        '''
        Test lookup with a domain name instead of an IP address.
        '''
        # Setup mock response
        mock_response = MagicMock()
        mock_response.text = json.dumps({
            'ipVersion': 4,
            'ipAddress': '**************',
            'latitude': 37.422,
            'longitude': -122.084,
            'countryName': 'United States of America',
            'countryCode': 'US',
            'timeZone': '-07:00',
            'cityName': 'Mountain View',
            'regionName': 'California',
            'isProxy': False,
            'continent': 'Americas',
            'continentCode': 'AM'
        })
        mock_response.raise_for_status.return_value = None

        # Setup mock client
        mock_client_instance = MagicMock()
        mock_client_instance.get.return_value = mock_response
        mock_client.return_value.__enter__.return_value = mock_client_instance

        # Call the function
        result = lookup_fullinfo('example.com', 5.0)

        # Verify the result
        self.assertEqual(result['ipAddress'], '**************')
        self.assertEqual(result['countryName'], 'United States of America')

class TestCentralizeText(TestCase):
    '''
    Tests for the centralize_text function.

    This test suite verifies that the function correctly centers text
    within the terminal width.
    '''

    @patch('base.utils.get_terminal_width')
    def test_text_shorter_than_terminal(self, mock_get_width):
        '''
        Test that text shorter than terminal width is properly centered.
        '''
        mock_get_width.return_value = 80
        text = 'Hello World'
        result = centralize_text(text)

        # Expected left margin is (80 - 11) / 2 = 34.5 -> 34 spaces
        expected = ' ' * 34 + 'Hello World'
        self.assertEqual(result, expected)

    @patch('base.utils.get_terminal_width')
    def test_text_equal_to_terminal(self, mock_get_width):
        '''
        Test that text equal to terminal width is returned as is.
        '''
        mock_get_width.return_value = 10
        text = 'HelloWorld'  # 10 characters
        result = centralize_text(text)

        # Blanks = 0, which is less than 2, so original text is returned
        self.assertEqual(result, text)

    @patch('base.utils.get_terminal_width')
    def test_text_longer_than_terminal(self, mock_get_width):
        '''
        Test that text longer than terminal width is returned as is.
        '''
        mock_get_width.return_value = 5
        text = 'Hello World'  # 11 characters
        result = centralize_text(text)

        # Blanks = -6, which is less than 2, so original text is returned
        self.assertEqual(result, text)

    @patch('base.utils.get_terminal_width')
    def test_empty_text(self, mock_get_width):
        '''
        Test that empty text is properly centered.
        '''
        mock_get_width.return_value = 80
        text = ''
        result = centralize_text(text)

        # Expected left margin is 80 / 2 = 40 spaces
        expected = ' ' * 40 + ''
        self.assertEqual(result, expected)

class TestEndOfCommandMsg(TestCase):
    '''
    Tests for the end_of_command_msg function.

    This test suite verifies that the function correctly formats and centralizes
    end-of-command messages.
    '''

    @patch('base.utils.centralize_text')
    @patch('base.utils.colorize')
    def test_end_of_command_msg_formatting(self, mock_colorize, mock_centralize_text):
        '''
        Test that the function correctly formats the end-of-command message.
        '''
        # Setup mocks
        mock_centralize_text.return_value = '     >>> END of test_command <<<'
        mock_colorize.return_value = 'COLORED_TEXT'

        # Create a mock command instance
        mock_command = MagicMock()

        # Call the function
        result = end_of_command_msg(mock_command, 'test_command')

        # Verify the result
        self.assertEqual(result, 'COLORED_TEXT')

        # Verify centralize_text was called with the correct message
        mock_centralize_text.assert_called_once_with('>>> END of test_command <<<')

        # Verify colorize was called with the correct parameters
        mock_colorize.assert_called_once_with(mock_command, 'command', '     >>> END of test_command <<<\n')

    @patch('base.utils.get_terminal_width')
    @patch('base.utils.colorize')
    def test_end_of_command_msg_integration(self, mock_colorize, mock_get_terminal_width):
        '''
        Test the integration of centralize_text within the end_of_command_msg function.
        '''
        # Setup mocks
        mock_get_terminal_width.return_value = 50
        mock_colorize.return_value = 'COLORED_TEXT'

        # Create a mock command instance
        mock_command = MagicMock()

        # Call the function
        result = end_of_command_msg(mock_command, 'test_command')

        # Verify the result
        self.assertEqual(result, 'COLORED_TEXT')

        # Verify colorize was called with a centralized message
        called_args = mock_colorize.call_args[0]
        self.assertEqual(called_args[0], mock_command)
        self.assertEqual(called_args[1], 'command')

        # The message should be centralized and have a newline at the end
        centralized_msg = called_args[2]
        self.assertTrue(centralized_msg.startswith(' '))  # Should have leading spaces
        self.assertTrue(centralized_msg.endswith('\n'))  # Should end with newline
        self.assertIn('>>> END of test_command <<<', centralized_msg)

class TestSourceLogInfoLine(TestCase):
    '''
    Tests for the source_log_info_line function.

    This test suite verifies that the function correctly formats information
    about source logs with proper alignment and sizing.
    '''

    @patch('base.utils.get_terminal_width')
    @patch('base.utils.get_size_of_source_log')
    @patch('base.utils.to_tilda')
    @patch('base.utils.convert_byte')
    def test_single_source_log(self, mock_convert_byte, mock_to_tilda, mock_get_size, mock_term_width):
        '''
        Test formatting for a single source log.
        '''
        # Setup mocks
        mock_term_width.return_value = 80
        mock_get_size.return_value = 1024
        mock_to_tilda.return_value = '~/logs/app.log'
        mock_convert_byte.return_value = '1.0 KB'

        result = source_log_info_line('/home/<USER>/logs/app.log', 1, 1)

        # Verify the result format
        self.assertIn('source log: ~/logs/app.log', result)
        self.assertIn('1.0 KB', result)
        mock_to_tilda.assert_called_once_with('/home/<USER>/logs/app.log')
        mock_get_size.assert_called_once_with('/home/<USER>/logs/app.log')

    @patch('base.utils.get_terminal_width')
    @patch('base.utils.get_size_of_source_log')
    @patch('base.utils.to_tilda')
    @patch('base.utils.convert_byte')
    def test_multiple_source_logs(self, mock_convert_byte, mock_to_tilda, mock_get_size, mock_term_width):
        '''
        Test formatting for a log that is part of multiple source logs.
        '''
        # Setup mocks
        mock_term_width.return_value = 80
        mock_get_size.return_value = 2048
        mock_to_tilda.return_value = '~/logs/app2.log'
        mock_convert_byte.return_value = '2.0 KB'

        result = source_log_info_line('/home/<USER>/logs/app2.log', 2, 5)

        # Verify the result format
        self.assertIn('source log: ~/logs/app2.log', result)
        self.assertIn('2.0 KB - 2/5 40%', result)
        mock_to_tilda.assert_called_once_with('/home/<USER>/logs/app2.log')
        mock_get_size.assert_called_once_with('/home/<USER>/logs/app2.log')

    @patch('base.utils.get_terminal_width')
    @patch('base.utils.get_size_of_source_log')
    @patch('base.utils.to_tilda')
    @patch('base.utils.convert_byte')
    def test_proper_spacing(self, mock_convert_byte, mock_to_tilda, mock_get_size, mock_term_width):
        '''
        Test that proper spacing is maintained between left and right parts.
        '''
        # Setup mocks
        mock_term_width.return_value = 50
        mock_get_size.return_value = 512
        mock_to_tilda.return_value = '~/log.txt'
        mock_convert_byte.return_value = '512 B'

        result = source_log_info_line('/home/<USER>/log.txt', 1, 1)

        # Calculate expected spacing
        left_part = 'source log: ~/log.txt'
        right_part = '512 B'
        expected_spaces = 50 - len(left_part) - len(right_part)

        # Verify spacing
        self.assertEqual(result, f'{left_part}{" " * expected_spaces}{right_part}')

class TestGetSumOfValues(TestCase):
    '''
    Tests for the get_sum_of_values function.

    This test suite verifies that the function correctly calculates the sum
    of all values in a dictionary.
    '''

    def test_empty_dictionary(self):
        '''
        Test that an empty dictionary returns 0.
        '''
        result = get_sum_of_values({})
        self.assertEqual(result, 0)

    def test_positive_values(self):
        '''
        Test with dictionary containing positive integer values.
        '''
        dictionary = {'a': 1, 'b': 2, 'c': 3}
        result = get_sum_of_values(dictionary)
        self.assertEqual(result, 6)

    def test_mixed_values(self):
        '''
        Test with dictionary containing both positive and negative integer values.
        '''
        dictionary = {'a': 5, 'b': -3, 'c': 2, 'd': -1}
        result = get_sum_of_values(dictionary)
        self.assertEqual(result, 3)

    def test_non_string_keys(self):
        '''
        Test with dictionary containing non-string keys.
        '''
        dictionary = {1: 10, 2.5: 20, (3, 4): 30}
        result = get_sum_of_values(dictionary)
        self.assertEqual(result, 60)

class TestTrimDict(TestCase):
    '''
    Tests for the trim_dict function.

    This test suite verifies that the function correctly trims a dictionary
    to a specified number of items.
    '''

    def test_empty_dict(self):
        '''
        Test that an empty dictionary returns an empty dictionary.
        '''
        result = trim_dict({}, 5)
        self.assertEqual(result, {})

    def test_dict_smaller_than_limit(self):
        '''
        Test that a dictionary with fewer items than the limit remains unchanged.
        '''
        input_dict = {'a': 1, 'b': 2, 'c': 3}
        result = trim_dict(input_dict, 5)
        self.assertEqual(result, input_dict)

    def test_dict_equal_to_limit(self):
        '''
        Test that a dictionary with exactly the limit number of items remains unchanged.
        '''
        input_dict = {'a': 1, 'b': 2, 'c': 3}
        result = trim_dict(input_dict, 3)
        self.assertEqual(result, input_dict)

    def test_dict_larger_than_limit(self):
        '''
        Test that a dictionary with more items than the limit is properly trimmed.
        '''
        input_dict = {'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5}
        expected = {'a': 1, 'b': 2, 'c': 3}
        result = trim_dict(input_dict, 3)
        self.assertEqual(result, expected)

    def test_with_different_key_types(self):
        '''
        Test that the function works with dictionaries containing different key types.
        '''
        input_dict = {1: 'one', 'two': 2, (3, 4): 'tuple'}
        expected = {1: 'one', 'two': 2}
        result = trim_dict(input_dict, 2)
        self.assertEqual(result, expected)

class TestTailFile(TestCase):
    '''
    Tests for the tail_file function.

    This test suite verifies that the function correctly reads the last N lines
    from a file and returns them in the correct order.
    '''

    def test_empty_file(self):
        '''
        Test that an empty file returns an empty list.
        '''
        mock_file = BytesIO(b'')
        with patch('builtins.open', return_value=mock_file):
            result = tail_file('dummy/path.txt', 5)
            self.assertEqual(result, [])

    def test_file_with_fewer_lines_than_requested(self):
        '''
        Test that requesting more lines than exist in the file returns all lines.
        '''
        content = b'Line 1\nLine 2\nLine 3'
        mock_file = BytesIO(content)

        with patch('builtins.open', return_value=mock_file):
            result = tail_file('dummy/path.txt', 5)
            self.assertEqual(result, list(reversed(['Line 1', 'Line 2', 'Line 3'])))

    def test_file_with_exact_lines_requested(self):
        '''
        Test that requesting exactly the number of lines in the file returns all lines.
        '''
        content = b'Line 1\nLine 2\nLine 3'
        mock_file = BytesIO(content)

        with patch('builtins.open', return_value=mock_file):
            result = tail_file('dummy/path.txt', 3)
            self.assertEqual(result, list(reversed(['Line 1', 'Line 2', 'Line 3'])))

    def test_file_with_more_lines_than_requested(self):
        '''
        Test that requesting fewer lines than exist in the file returns only the last N lines.
        '''
        content = b'Line 1\nLine 2\nLine 3\nLine 4\nLine 5'
        mock_file = BytesIO(content)

        with patch('builtins.open', return_value=mock_file):
            result = tail_file('dummy/path.txt', 3)
            self.assertEqual(result, list(reversed(['Line 3', 'Line 4', 'Line 5'])))

    def test_file_with_empty_lines(self):
        '''
        Test that empty lines are properly handled.
        '''
        content = b'Line 1\n\nLine 3\n\nLine 5'
        mock_file = BytesIO(content)

        with patch('builtins.open', return_value=mock_file):
            result = tail_file('dummy/path.txt', 3)
            self.assertEqual(result, list(reversed(['Line 3', '', 'Line 5'])))

    def test_file_with_non_utf8_content(self):
        '''
        Test that the function handles non-UTF-8 content gracefully.
        '''
        # Create content with non-UTF-8 bytes
        content = b'Line 1\nLine 2\n\xff\xfe Invalid UTF-8\nLine 4'
        mock_file = BytesIO(content)

        with patch('builtins.open', return_value=mock_file):
            with self.assertRaises(UnicodeDecodeError):
                tail_file('dummy/path.txt', 3)

class TestStripProtocolAndPathFromUrl(TestCase):
    '''
    Tests for the strip_protocol_and_path_from_url function.

    This test suite verifies that the function correctly extracts the domain
    from URLs by removing protocols, paths, and query parameters.
    '''

    def test_url_with_https_protocol_and_path(self):
        '''
        Test that a URL with HTTPS protocol and path returns only the domain.
        '''
        url = 'https://example.com/path/to/resource?query=param'
        result = strip_protocol_and_path_from_url(url)
        self.assertEqual(result, 'example.com')

    def test_url_with_http_protocol(self):
        '''
        Test that a URL with HTTP protocol returns only the domain.
        '''
        url = 'http://example.com'
        result = strip_protocol_and_path_from_url(url)
        self.assertEqual(result, 'example.com')

    def test_url_with_trailing_slash(self):
        '''
        Test that a URL with a trailing slash returns the domain without the slash.
        '''
        url = 'https://example.com/'
        result = strip_protocol_and_path_from_url(url)
        self.assertEqual(result, 'example.com')

    def test_url_with_query_parameters(self):
        '''
        Test that a URL with query parameters returns only the domain.
        '''
        url = 'http://example.com?query=param'
        result = strip_protocol_and_path_from_url(url)
        self.assertEqual(result, 'example.com')

    def test_url_with_subdomain(self):
        '''
        Test that a URL with a subdomain returns the full domain including the subdomain.
        '''
        url = 'https://sub.example.com/path'
        result = strip_protocol_and_path_from_url(url)
        self.assertEqual(result, 'sub.example.com')

    def test_url_with_port(self):
        '''
        Test that a URL with a port number returns the domain with the port.
        '''
        url = 'https://example.com:8080/path'
        result = strip_protocol_and_path_from_url(url)
        self.assertEqual(result, 'example.com:8080')

    def test_domain_only(self):
        '''
        Test that a string with just a domain returns the same domain.
        '''
        url = 'example.com'
        result = strip_protocol_and_path_from_url(url)
        self.assertEqual(result, 'example.com')

class TestIsCidr(TestCase):
    '''
    Tests for the is_cidr function.

    This test suite verifies that the function correctly identifies valid CIDR notations.
    '''

    def test_valid_cidr(self):
        '''
        Test that a valid CIDR notation returns True.
        '''

        for string in [
            '***********/24',
            '10.0.0.0/8',
            '**********/12',
            '0.0.0.0/0',
            '***********/32',
            '2001:db8::/32',
            'fe80::/10',
            '::1/128',
        ]:
            self.assertTrue(is_cidr(string))

    def test_invalid_cidr(self):
        '''
        Test that an invalid CIDR notation returns False.
        '''

        for string in [
            '***********',
            '2001:db8::',
            '***********/33',
            '2001:db8::/129',
            'not-an-ip/24',
            '***********/-1',
            '***********/abc',
            '',
        ]:
            self.assertFalse(is_cidr(string))

class TestIsIpV4(TestCase):
    '''
    Tests for the is_ip_v4 function.

    This test suite verifies that the function correctly identifies valid IPv4 addresses.
    '''

    def test_valid_ip_v4(self):
        '''
        Test that a valid IPv4 address returns True.
        '''

        for string in [
            '***********',
            '***************',
        ]:
            self.assertTrue(is_ip_v4(string))

    def test_invalid_ip_v4(self):
        '''
        Test that an invalid IPv4 address returns False.
        '''

        for string in [
            '256.256.256.256',
            '192.168.1',
            'abc.def.ghi.jkl',
            '',
        ]:
            self.assertFalse(is_ip_v4(string))

class TestIsIpV6(TestCase):
    '''
    Tests for the is_ip_v6 function.

    This test suite verifies that the function correctly identifies valid IPv6 addresses.
    '''

    def test_valid_ip_v6(self):
        '''
        Test that a valid IPv6 address returns True.
        '''

        for string in [
            '2001:0db8:85a3:0000:0000:8a2e:0370:7334',
            '2001:db8:85a3::8a2e:370:7334',
            '::1',
            '::',
            '2001:db8::',
            'fe80::',
            'fe80::1',
            'fe80::215:5dff:fe00:402',
            '::ffff:***********',
            '64:ff9b::**********',
            '2001:db8:3:4::**********',
        ]:
            self.assertTrue(is_ip_v6(string))

    def test_invalid_ip_v6(self):
        '''
        Test that an invalid IPv6 address returns False.
        '''

        for string in [
            '1200::AB00:1234::2552:7777:1313',
            '***********',
            '2001:db8::/32',
            'fe80::/10',
            '::1/128',
            'hello',
            '',
        ]:
            self.assertFalse(is_ip_v6(string))

class TestIsPrivateV4(TestCase):
    '''
    Tests for the is_private_v4 function.

    This test suite verifies that the function correctly identifies private IPv4 addresses.
    '''

    def test_valid_private_v4(self):
        '''
        Test that a valid private IPv4 address returns True.
        '''

        for string in [
            '********',
            '**************',
            '**********',
            '***********',
            '**************',
            '***********',
            '***************',
        ]:
            self.assertTrue(is_private_v4(string))

    def test_invalid_private_v4(self):
        '''
        Test that an invalid private IPv4 address returns False.
        '''

        for string in [
            '127.0.0.1',
            '***********',
            '**********',
            '**********',
            '***********',
            '***********',
            '*******',
            '***********',
            'invalid-ip',
            '',
        ]:
            self.assertFalse(is_private_v4(string))

class TestIsPrivateV6(TestCase):
    '''
    Tests for the is_private_v6 function.

    This test suite verifies that the function correctly identifies private IPv6 addresses.
    '''

    def test_valid_private_v6(self):
        '''
        Test that a valid private IPv6 address returns True.
        '''

        for string in [
            'fc00::1',
            'fd12:3456:789a:1::1',
            'fdff:ffff:ffff:ffff::1',
            'fe80::1',
            'fe80::215:5dff:fe00:402',
            '::1',
        ]:
            self.assertTrue(is_private_v6(string))

    def test_invalid_private_v6(self):
        '''
        Test that an invalid private IPv6 address returns False.
        '''

        for string in [
            '::ffff:127.0.0.1',
            '2001:db8::1',
            '2001:4860:4860::8888',
            '2606:4700:4700::1111',
            'ff02::1',
            'invalid-ipv6',
            '',
        ]:
            self.assertFalse(is_private_v6(string))

class TestGetHosts(TestCase):
    '''
    Tests for the get_hosts function.

    This test suite verifies that the function correctly returns a list of host IP addresses
    within a given network, and handles edge cases appropriately.
    '''

    def test_valid_ipv4_network(self):
        '''
        Test that a valid IPv4 network returns the correct list of host addresses.
        '''
        result = get_hosts('***********/30')
        self.assertEqual(result, ['***********', '***********'])

    def test_single_host_network(self):
        '''
        Test that a /32 network (single host) returns an empty list since there are no host addresses.
        '''
        result = get_hosts('********/32')
        self.assertEqual(result, ['********'])

    def test_larger_network(self):
        '''
        Test that a larger network returns the correct number of host addresses.
        '''
        result = get_hosts('***********/29')
        self.assertEqual(result, ['***********', '***********', '***********', '***********', '***********', '***********'])

    def test_ipv6_network(self):
        '''
        Test that an IPv6 network returns the correct list of host addresses.
        '''
        result = get_hosts('2001:db8::/126')
        self.assertEqual(result, ['2001:db8::1', '2001:db8::2', '2001:db8::3'])

    def test_invalid_ip(self):
        '''
        Test that an invalid IP address returns an empty list.
        '''
        result = get_hosts('invalid_ip')
        self.assertEqual(result, [])

    def test_invalid_cidr(self):
        '''
        Test that an invalid CIDR notation returns an empty list.
        '''
        result = get_hosts('***********/33')
        self.assertEqual(result, [])

class TestNormalizeDnsQuestionName(TestCase):
    '''
    Tests for the normalize_dns_question_name function.

    This test suite verifies that the function correctly normalizes DNS question names
    by replacing numbers in parentheses with dots and removing leading/trailing dots.
    '''

    def test_simple_replacement(self):
        '''
        Test that numbers in parentheses are replaced with dots.
        '''
        result = normalize_dns_question_name('example(123)com')
        self.assertEqual(result, 'example.com')

    def test_leading_parentheses(self):
        '''
        Test that leading parentheses are replaced with dots and then stripped.
        '''
        result = normalize_dns_question_name('(456)example(789)com')
        self.assertEqual(result, 'example.com')

    def test_trailing_parentheses(self):
        '''
        Test that trailing parentheses are replaced with dots and then stripped.
        '''
        result = normalize_dns_question_name('example(123)com(456)')
        self.assertEqual(result, 'example.com')

    def test_multiple_parentheses(self):
        '''
        Test that multiple sets of parentheses are all replaced correctly.
        '''
        result = normalize_dns_question_name('sub(1)domain(2)example(3)com')
        self.assertEqual(result, 'sub.domain.example.com')

    def test_no_parentheses(self):
        '''
        Test that a string without parentheses remains unchanged.
        '''
        result = normalize_dns_question_name('example.com')
        self.assertEqual(result, 'example.com')

    def test_empty_string(self):
        '''
        Test that an empty string returns an empty string.
        '''
        result = normalize_dns_question_name('')
        self.assertEqual(result, '')

    def test_only_parentheses(self):
        '''
        Test that a string with only parenthesized numbers returns an empty string.
        '''
        result = normalize_dns_question_name('(123)(456)(789)')
        self.assertEqual(result, '')

class TestNormalizeDate(TestCase):
    '''
    Tests for the normalize_date function.

    This test suite verifies that the function correctly normalizes date strings
    to the 'YYYY-MM-DD' format.
    '''

    def test_mm_dd_yyyy_format(self):
        '''
        Test that dates in MM/DD/YYYY format are correctly normalized.
        '''
        result = normalize_date('12/8/2020')
        self.assertEqual(result, '2020-12-08')

        result = normalize_date('12/31/2020')
        self.assertEqual(result, '2020-12-31')

        result = normalize_date('01/01/2000')
        self.assertEqual(result, '2000-01-01')

        result = normalize_date('12/31/1999')
        self.assertEqual(result, '1999-12-31')

    def test_mm_dd_yy_format(self):
        '''
        Test that dates in MM/DD/YY format are correctly normalized.
        '''
        result = normalize_date('12/8/20')
        self.assertEqual(result, '2020-12-08')

        result = normalize_date('12/31/20')
        self.assertEqual(result, '2020-12-31')

        result = normalize_date('01/01/00')
        self.assertEqual(result, '2000-01-01')

        result = normalize_date('12/31/99')
        self.assertEqual(result, '1999-12-31')

    def test_invalid_format(self):
        '''
        Test that dates in unsupported formats are returned unchanged.
        '''
        result = normalize_date('31/12/2020')
        self.assertEqual(result, '31/12/2020')

        result = normalize_date('2020-12-31')
        self.assertEqual(result, '2020-12-31')

        result = normalize_date('invalid date')
        self.assertEqual(result, 'invalid date')

        result = normalize_date('')
        self.assertEqual(result, '')

class TestNormalizeTime(TestCase):
    '''
    Tests for the normalize_time function.

    This test suite verifies that the function correctly normalizes time strings
    to the 24-hour format 'HH:MM:SS'.
    '''

    def test_12_hour_format_am(self):
        '''
        Test normalization of 12-hour format AM times.
        '''
        self.assertEqual(normalize_time('12:00:00 AM'), '00:00:00')
        self.assertEqual(normalize_time('01:30:45 AM'), '01:30:45')
        self.assertEqual(normalize_time('11:59:59 AM'), '11:59:59')

    def test_12_hour_format_pm(self):
        '''
        Test normalization of 12-hour format PM times.
        '''
        self.assertEqual(normalize_time('12:00:00 PM'), '12:00:00')
        self.assertEqual(normalize_time('01:30:45 PM'), '13:30:45')
        self.assertEqual(normalize_time('11:59:59 PM'), '23:59:59')

    def test_single_digit_hours(self):
        '''
        Test normalization of times with single-digit hours.
        '''
        self.assertEqual(normalize_time('1:15:30 AM'), '01:15:30')
        self.assertEqual(normalize_time('9:45:00 PM'), '21:45:00')

    def test_24_hour_format_with_am_pm(self):
        '''
        Test normalization of 24-hour format times with AM/PM.
        '''
        self.assertEqual(normalize_time('00:00:00 AM'), '00:00:00')
        self.assertEqual(normalize_time('13:30:45 PM'), '13:30:45')

    def test_invalid_time_formats(self):
        '''
        Test that invalid time formats return the original string.
        '''
        self.assertEqual(normalize_time('25:00:00 PM'), '25:00:00 PM')
        self.assertEqual(normalize_time('12:60:00 AM'), '12:60:00 AM')
        self.assertEqual(normalize_time('not a time'), 'not a time')
        self.assertEqual(normalize_time('12:00:00'), '12:00:00')

class TestNormalizeWindowsserverCategory(TestCase):
    '''
    Tests for the normalize_windowsserver_category function.

    This test suite verifies that the function correctly normalizes Windows Server
    category strings by converting them to lowercase and removing non-alphanumeric characters.
    '''

    def test_with_slashes(self):
        '''
        Test that slashes are removed from the category string.
        '''
        result = normalize_windowsserver_category('Logon/Logoff')
        self.assertEqual(result, 'logonlogoff')

    def test_with_spaces(self):
        '''
        Test that spaces are removed from the category string.
        '''
        result = normalize_windowsserver_category('Account Management')
        self.assertEqual(result, 'accountmanagement')

    def test_with_hyphens(self):
        '''
        Test that hyphens are removed from the category string.
        '''
        result = normalize_windowsserver_category('ds-access')
        self.assertEqual(result, 'dsaccess')

    def test_with_mixed_characters(self):
        '''
        Test that all non-alphanumeric characters are removed from the category string.
        '''
        result = normalize_windowsserver_category('Security-Alert: #123_Warning!')
        self.assertEqual(result, 'securityalert123warning')

    def test_empty_string(self):
        '''
        Test that an empty string returns an empty string.
        '''
        result = normalize_windowsserver_category('')
        self.assertEqual(result, '')

class TestGetParsedDirs(TestCase):
    '''
    Tests for the get_parsed_dirs function.

    This test suite verifies that the function correctly retrieves and sorts
    directories that match the YYYY-MM-DD format.
    '''

    @patch('base.utils.path.exists')
    @patch('base.utils.listdir')
    @patch('base.utils.path.isdir')
    @patch('base.utils.is_ymd')
    def test_nonexistent_directory(self, mock_is_ymd, mock_isdir, mock_listdir, mock_exists):
        '''
        Test that an empty list is returned when the directory does not exist.
        '''
        mock_exists.return_value = False

        result = get_parsed_dirs('/nonexistent/directory', False)

        self.assertEqual(result, [])
        mock_exists.assert_called_once_with('/nonexistent/directory')
        mock_listdir.assert_not_called()

    @patch('base.utils.path.exists')
    @patch('base.utils.listdir')
    @patch('base.utils.path.isdir')
    @patch('base.utils.is_ymd')
    def test_empty_directory(self, mock_is_ymd, mock_isdir, mock_listdir, mock_exists):
        '''
        Test that an empty list is returned when the directory exists but is empty.
        '''
        mock_exists.return_value = True
        mock_listdir.return_value = []

        result = get_parsed_dirs('/empty/directory', False)

        self.assertEqual(result, [])
        mock_exists.assert_called_once_with('/empty/directory')
        mock_listdir.assert_called_once_with('/empty/directory')

    @patch('base.utils.path.exists')
    @patch('base.utils.listdir')
    @patch('base.utils.path.isdir')
    @patch('base.utils.is_ymd')
    def test_directory_with_valid_and_invalid_entries(self, mock_is_ymd, mock_isdir, mock_listdir, mock_exists):
        '''
        Test that only valid YYYY-MM-DD formatted directories are returned.
        '''
        mock_exists.return_value = True
        mock_listdir.return_value = ['2023-05-13', 'invalid-dir', '2023-05-15', 'another-invalid']

        # Configure is_ymd to return True only for valid date formats
        def is_ymd_side_effect(value):
            return value in ['2023-05-13', '2023-05-15']
        mock_is_ymd.side_effect = is_ymd_side_effect

        # Configure isdir to return True for all entries
        mock_isdir.return_value = True

        result = get_parsed_dirs('/test/directory', False)

        self.assertEqual(result, ['2023-05-13', '2023-05-15'])
        mock_exists.assert_called_once_with('/test/directory')
        mock_listdir.assert_called_once_with('/test/directory')

    @patch('base.utils.path.exists')
    @patch('base.utils.listdir')
    @patch('base.utils.path.isdir')
    @patch('base.utils.is_ymd')
    def test_directory_with_non_directory_entries(self, mock_is_ymd, mock_isdir, mock_listdir, mock_exists):
        '''
        Test that only directory entries are included in the result.
        '''
        mock_exists.return_value = True
        mock_listdir.return_value = ['2023-05-13', '2023-05-14', '2023-05-15']

        # All entries are valid date formats
        mock_is_ymd.return_value = True

        # Configure isdir to return True only for some entries
        def isdir_side_effect(path):
            return '14' not in path  # '2023-05-14' is not a directory
        mock_isdir.side_effect = isdir_side_effect

        result = get_parsed_dirs('/test/directory', False)

        self.assertEqual(result, ['2023-05-13', '2023-05-15'])
        mock_exists.assert_called_once_with('/test/directory')
        mock_listdir.assert_called_once_with('/test/directory')

    @patch('base.utils.path.exists')
    @patch('base.utils.listdir')
    @patch('base.utils.path.isdir')
    @patch('base.utils.is_ymd')
    def test_sorting_order(self, mock_is_ymd, mock_isdir, mock_listdir, mock_exists):
        '''
        Test that directories are sorted in natural order.
        '''
        mock_exists.return_value = True
        # Intentionally provide in non-sorted order
        mock_listdir.return_value = ['2023-05-15', '2023-05-13', '2023-05-14']
        mock_is_ymd.return_value = True
        mock_isdir.return_value = True

        # Test ascending order
        result = get_parsed_dirs('/test/directory', False)
        self.assertEqual(result, ['2023-05-13', '2023-05-14', '2023-05-15'])

        # Test descending order
        result = get_parsed_dirs('/test/directory', True)
        self.assertEqual(result, ['2023-05-15', '2023-05-14', '2023-05-13'])

class TestListOfTuplesToList(TestCase):
    '''
    Tests for the list_of_tuples_to_list function.

    This test suite verifies that the function correctly converts a list of tuples
    into a flat list, particularly for single-item tuples.
    '''

    def test_empty_list(self):
        '''
        Test that an empty list returns an empty list.
        '''
        result = list_of_tuples_to_list([])
        self.assertEqual(result, [])

    def test_single_item_tuples(self):
        '''
        Test that a list of single-item tuples is correctly flattened.
        '''
        input_list = [(1,), (2,), (3,)]
        result = list_of_tuples_to_list(input_list)
        self.assertEqual(result, [1, 2, 3])

    def test_mixed_type_tuples(self):
        '''
        Test that a list of single-item tuples with mixed types is correctly flattened.
        '''
        input_list = [(1,), ('string',), (True,), (None,)]
        result = list_of_tuples_to_list(input_list)
        self.assertEqual(result, [1, 'string', True, None])

    def test_multi_item_tuples(self):
        '''
        Test behavior with multi-item tuples (not the intended use case).
        '''
        input_list = [(1, 2), (3, 4)]
        result = list_of_tuples_to_list(input_list)
        # Function uses chain(*lst), so multi-item tuples will be flattened completely
        self.assertEqual(result, [1, 2, 3, 4])

class TestGetDateOfSourceLog(TestCase):
    '''
    Tests for the get_date_of_source_log function.

    This test suite verifies that the function correctly extracts the date
    from log file names in the format 'YYYY-MM-DD--Day.log'.
    '''

    def test_standard_log_path(self):
        '''
        Test that the function correctly extracts the date from a standard log path.
        '''
        result = get_date_of_source_log('/FOO/BAR/BAZ/2023-05-12--Fri.log')
        self.assertEqual(result, '2023-05-12')

    def test_different_directory_paths(self):
        '''
        Test that the function works with different directory paths.
        '''
        result = get_date_of_source_log('/var/logs/2022-11-23--Wed.log')
        self.assertEqual(result, '2022-11-23')

        result = get_date_of_source_log('/logs/2021-01-01--Fri.log')
        self.assertEqual(result, '2021-01-01')

    def test_filename_only(self):
        '''
        Test that the function works with just a filename without a directory path.
        '''
        result = get_date_of_source_log('2023-12-25--Mon.log')
        self.assertEqual(result, '2023-12-25')

    def test_with_different_day_formats(self):
        '''
        Test that the function works with different day abbreviations.
        '''
        result = get_date_of_source_log('/logs/2023-06-15--Thu.log')
        self.assertEqual(result, '2023-06-15')

        result = get_date_of_source_log('/logs/2023-07-22--Sat.log')
        self.assertEqual(result, '2023-07-22')

        result = get_date_of_source_log('/logs/2023-07-23--Sun.log')
        self.assertEqual(result, '2023-07-23')

class TestGetSizeOfSourceLog(TestCase):
    '''
    Tests for the get_size_of_source_log function.

    This test suite verifies that the function correctly returns the size of a log file
    or 0 if the file doesn't exist or an error occurs.
    '''

    def test_existing_file(self):
        '''
        Test that the function returns the correct size for an existing file.
        '''
        with TemporaryDirectory() as temp_dir:
            # Create a temporary file with known content
            file_path = f'{temp_dir}/test.log'
            test_content = b'Test log content'

            with open(file_path, 'wb') as f:
                f.write(test_content)

            # Get the size and verify it matches the content length
            result = get_size_of_source_log(file_path)
            self.assertEqual(result, len(test_content))

    def test_nonexistent_file(self):
        '''
        Test that the function returns 0 for a nonexistent file.
        '''
        result = get_size_of_source_log('/path/to/nonexistent/file.log')
        self.assertEqual(result, 0)

    @patch('base.utils.Path')
    def test_error_handling(self, mock_path):
        '''
        Test that the function returns 0 when an exception occurs.
        '''
        # Setup mock to raise an exception
        mock_path_instance = MagicMock()
        mock_path_instance.stat.side_effect = Exception('Test exception')
        mock_path.return_value = mock_path_instance

        result = get_size_of_source_log('/path/to/file.log')
        self.assertEqual(result, 0)

class TestGetRandomWallpaper(TestCase):
    '''
    Tests for the get_random_wallpaper function.

    This test suite verifies that the function correctly returns a random wallpaper path
    from the available wallpapers in the static directory.
    '''

    @patch('base.utils.get_list_of_files')
    @patch('base.utils.choice')
    def test_get_random_wallpaper(self, mock_choice, mock_get_list_of_files):
        '''
        Test that the function returns the correct path for a random wallpaper.
        '''
        ## setup mocks
        mock_get_list_of_files.return_value = [
            '/project/static/files/img-wallpapers/42.jpg',
            '/project/static/files/img-wallpapers/63.jpg'
        ]
        mock_choice.return_value = '/project/static/files/img-wallpapers/63.jpg'

        ## call the function
        result = get_random_wallpaper()

        ## verify the result
        self.assertEqual(result, '/static/files/img-wallpapers/63.jpg')

        ## verify the mocks were called correctly
        mock_get_list_of_files.assert_called_once()
        mock_choice.assert_called_once_with(mock_get_list_of_files.return_value)

class TestGetNameOfFunction(TestCase):
    '''
    Tests for the get_name_of_function function.

    This test suite verifies that the function correctly returns the name
    of the calling function.
    '''

    def test_direct_call(self):
        '''
        Test that the function returns the correct name when called directly.
        '''
        result = get_name_of_function()
        self.assertEqual(result, 'test_direct_call')

    def test_nested_function_call(self):
        '''
        Test that the function returns the name of the immediate calling function
        when called from a nested function.
        '''
        def inner_function():
            return get_name_of_function()

        result = inner_function()
        self.assertEqual(result, 'inner_function')

    def test_lambda_function(self):
        '''
        Test that the function works with lambda functions.
        '''
        lambda_func = lambda: get_name_of_function()
        result = lambda_func()
        self.assertEqual(result, '<lambda>')

    def test_method_in_class(self):
        '''
        Test that the function works when called from a method in a class.
        '''
        class TestClass:
            def test_method(self):
                return get_name_of_function()

        instance = TestClass()
        result = instance.test_method()
        self.assertEqual(result, 'test_method')

class TestYmdToYm(TestCase):
    '''
    Tests for the ymd_to_ym function.

    This test suite verifies that the function correctly converts date strings
    from 'YYYY-MM-DD' format to 'YYYY-MM' format.
    '''

    def test_standard_date_format(self):
        '''
        Test that standard date formats are correctly converted.
        '''
        result = ymd_to_ym('2023-10-05')
        self.assertEqual(result, '2023-10')

        result = ymd_to_ym('1999-12-31')
        self.assertEqual(result, '1999-12')

        result = ymd_to_ym('2023_10_05')
        self.assertEqual(result, '2023_10')

        result = ymd_to_ym('1999_12_31')
        self.assertEqual(result, '1999_12')

    def test_edge_cases(self):
        '''
        Test edge cases like single-digit days.
        '''
        result = ymd_to_ym('2023-01-01')
        self.assertEqual(result, '2023-01')

        result = ymd_to_ym('2023-05-09')
        self.assertEqual(result, '2023-05')

        result = ymd_to_ym('2023_01_01')
        self.assertEqual(result, '2023_01')

        result = ymd_to_ym('2023_05_09')
        self.assertEqual(result, '2023_05')

    def test_invalid_input(self):
        '''
        Test that the function handles invalid input appropriately.
        '''
        # Input without day part
        result = ymd_to_ym('2023-10')
        self.assertEqual(result, '2023-10')

        # Input without day part
        result = ymd_to_ym('2023_10')
        self.assertEqual(result, '2023_10')

        # Input with non-standard format
        result = ymd_to_ym('20231005')
        self.assertEqual(result, '20231005')

        # Empty string
        result = ymd_to_ym('')
        self.assertEqual(result, '')

class TestFilterList(TestCase):
    '''
    Tests for the filter_list function.

    This test suite verifies that the function correctly filters lists of items
    based on various date criteria.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        # List of date strings
        self.date_list = [
            '2023-01-15',
            '2023-02-10',
            '2023-03-05',
            '2023-04-20'
        ]

        # List of log paths
        self.log_path_list = [
            '/home/<USER>/logs/2023-01-15--Mon.log',
            '/home/<USER>/logs/2023-02-10--Fri.log',
            '/home/<USER>/logs/2023-03-05--Sun.log',
            '/home/<USER>/logs/2023-04-20--Thu.log'
        ]

    def test_filter_by_year_months(self):
        '''
        Test filtering by year_months parameter.
        '''
        # Test with date strings
        result = filter_list(
            list_of_items=self.date_list,
            year_months=['2023-01', '2023-03']
        )
        self.assertEqual(result, ['2023-01-15', '2023-03-05'])

        # Test with log paths
        result = filter_list(
            list_of_items=self.log_path_list,
            year_months=['2023-01', '2023-03']
        )
        expected = [
            '/home/<USER>/logs/2023-01-15--Mon.log',
            '/home/<USER>/logs/2023-03-05--Sun.log'
        ]
        self.assertEqual(result, expected)

    def test_filter_by_year_month_days(self):
        '''
        Test filtering by year_month_days parameter.
        '''
        # Test with date strings
        result = filter_list(
            list_of_items=self.date_list,
            year_month_days=['2023-02-10', '2023-04-20']
        )
        self.assertEqual(result, ['2023-02-10', '2023-04-20'])

        # Test with log paths
        result = filter_list(
            list_of_items=self.log_path_list,
            year_month_days=['2023-02-10', '2023-04-20']
        )
        expected = [
            '/home/<USER>/logs/2023-02-10--Fri.log',
            '/home/<USER>/logs/2023-04-20--Thu.log'
        ]
        self.assertEqual(result, expected)

    def test_filter_by_start_year_month(self):
        '''
        Test filtering by start_year_month parameter.
        '''
        # Test with date strings
        result = filter_list(
            list_of_items=self.date_list,
            start_year_month='2023-03'
        )
        self.assertEqual(result, ['2023-03-05', '2023-04-20'])

        # Test with log paths
        result = filter_list(
            list_of_items=self.log_path_list,
            start_year_month='2023-03'
        )
        expected = [
            '/home/<USER>/logs/2023-03-05--Sun.log',
            '/home/<USER>/logs/2023-04-20--Thu.log'
        ]
        self.assertEqual(result, expected)

    def test_filter_by_end_year_month(self):
        '''
        Test filtering by end_year_month parameter.
        '''
        # Test with date strings
        result = filter_list(
            list_of_items=self.date_list,
            end_year_month='2023-02'
        )
        self.assertEqual(result, ['2023-01-15', '2023-02-10'])

        # Test with log paths
        result = filter_list(
            list_of_items=self.log_path_list,
            end_year_month='2023-02'
        )
        expected = [
            '/home/<USER>/logs/2023-01-15--Mon.log',
            '/home/<USER>/logs/2023-02-10--Fri.log'
        ]
        self.assertEqual(result, expected)

    def test_filter_by_start_and_end_year_month(self):
        '''
        Test filtering by both start_year_month and end_year_month parameters.
        '''
        # Test with date strings
        result = filter_list(
            list_of_items=self.date_list,
            start_year_month='2023-02',
            end_year_month='2023-03'
        )
        self.assertEqual(result, ['2023-02-10', '2023-03-05'])

        # Test with log paths
        result = filter_list(
            list_of_items=self.log_path_list,
            start_year_month='2023-02',
            end_year_month='2023-03'
        )
        expected = [
            '/home/<USER>/logs/2023-02-10--Fri.log',
            '/home/<USER>/logs/2023-03-05--Sun.log'
        ]
        self.assertEqual(result, expected)

    def test_filter_by_start_year_month_day(self):
        '''
        Test filtering by start_year_month_day parameter.
        '''
        # Test with date strings
        result = filter_list(
            list_of_items=self.date_list,
            start_year_month_day='2023-02-10'
        )
        self.assertEqual(result, ['2023-02-10', '2023-03-05', '2023-04-20'])

        # Test with log paths
        result = filter_list(
            list_of_items=self.log_path_list,
            start_year_month_day='2023-02-10'
        )
        expected = [
            '/home/<USER>/logs/2023-02-10--Fri.log',
            '/home/<USER>/logs/2023-03-05--Sun.log',
            '/home/<USER>/logs/2023-04-20--Thu.log'
        ]
        self.assertEqual(result, expected)

    def test_filter_by_end_year_month_day(self):
        '''
        Test filtering by end_year_month_day parameter.
        '''
        # Test with date strings
        result = filter_list(
            list_of_items=self.date_list,
            end_year_month_day='2023-03-05'
        )
        self.assertEqual(result, ['2023-01-15', '2023-02-10', '2023-03-05'])

        # Test with log paths
        result = filter_list(
            list_of_items=self.log_path_list,
            end_year_month_day='2023-03-05'
        )
        expected = [
            '/home/<USER>/logs/2023-01-15--Mon.log',
            '/home/<USER>/logs/2023-02-10--Fri.log',
            '/home/<USER>/logs/2023-03-05--Sun.log'
        ]
        self.assertEqual(result, expected)

    def test_filter_by_start_and_end_year_month_day(self):
        '''
        Test filtering by both start_year_month_day and end_year_month_day parameters.
        '''
        # Test with date strings
        result = filter_list(
            list_of_items=self.date_list,
            start_year_month_day='2023-02-10',
            end_year_month_day='2023-03-05'
        )
        self.assertEqual(result, ['2023-02-10', '2023-03-05'])

        # Test with log paths
        result = filter_list(
            list_of_items=self.log_path_list,
            start_year_month_day='2023-02-10',
            end_year_month_day='2023-03-05'
        )
        expected = [
            '/home/<USER>/logs/2023-02-10--Fri.log',
            '/home/<USER>/logs/2023-03-05--Sun.log'
        ]
        self.assertEqual(result, expected)

    def test_empty_list(self):
        '''
        Test with an empty list of items.
        '''
        # Empty items list
        result = filter_list(
            list_of_items=[],
            year_months=['2023-01']
        )
        self.assertEqual(result, [])

        # No filter criteria
        result = filter_list(
            list_of_items=self.log_path_list,
        )
        # Should return all items, sorted
        self.assertEqual(result, natsorted(set(self.log_path_list)))

    def test_no_matching_items(self):
        '''
        Test when no items match the filter criteria.
        '''
        result = filter_list(
            list_of_items=self.date_list,
            year_months=['2022-12']
        )
        self.assertEqual(result, [])

class TestFilterDatabases(TestCase):
    '''
    Tests for the filter_databases function.

    This test suite verifies that the function correctly filters database names
    based on various date criteria.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.databases = [
            'daemon__Sensor1__2024_01_15',
            'daemon__Sensor1__2024_02_10',
            'daemon__Sensor2__2024_02_20',
            'daemon__Sensor1__2024_03_05',
            'daemon__Sensor2__2024_03_15',
            'daemon__Sensor1__2024_04_01',
            'geolocation',  # Non-dated database
            'malicious'  # Non-dated database
        ]

    def tearDown(self):
        '''
        Clean up after tests.
        '''
        self.databases = None

    def test_filter_by_year_months(self):
        '''
        Test filtering databases by year_months parameter.
        '''
        result = filter_databases(
            databases=self.databases,
            year_months=['2024_02']
        )
        expected = [
            'daemon__Sensor1__2024_02_10',
            'daemon__Sensor2__2024_02_20'
        ]
        self.assertEqual(result, natsorted(set(expected)))

        result = filter_databases(
            databases=self.databases,
            year_months=['2024_02', '2024_04']
        )
        expected = [
            'daemon__Sensor1__2024_02_10',
            'daemon__Sensor2__2024_02_20',
            'daemon__Sensor1__2024_04_01'
        ]
        self.assertEqual(result, natsorted(set(expected)))

    def test_filter_by_year_month_days(self):
        '''
        Test filtering databases by year_month_days parameter.
        '''
        result = filter_databases(
            databases=self.databases,
            year_month_days=['2024_02_20']
        )
        expected = ['daemon__Sensor2__2024_02_20']
        self.assertEqual(result, natsorted(set(expected)))

        result = filter_databases(
            databases=self.databases,
            year_month_days=['2024_02_20', '2024_04_01']
        )
        expected = [
            'daemon__Sensor2__2024_02_20',
            'daemon__Sensor1__2024_04_01'
        ]
        self.assertEqual(result, natsorted(set(expected)))

    def test_filter_by_start_year_month(self):
        '''
        Test filtering databases by start_year_month parameter.
        '''
        result = filter_databases(
            databases=self.databases,
            start_year_month='2024_03'
        )
        expected = [
            'daemon__Sensor1__2024_03_05',
            'daemon__Sensor2__2024_03_15',
            'daemon__Sensor1__2024_04_01'
        ]
        self.assertEqual(result, natsorted(set(expected)))

    def test_filter_by_end_year_month(self):
        '''
        Test filtering databases by end_year_month parameter.
        '''
        result = filter_databases(
            databases=self.databases,
            end_year_month='2024_02'
        )
        expected = [
            'daemon__Sensor1__2024_01_15',
            'daemon__Sensor1__2024_02_10',
            'daemon__Sensor2__2024_02_20'
        ]
        self.assertEqual(result, natsorted(set(expected)))

    def test_filter_by_date_range_year_month(self):
        '''
        Test filtering databases by both start_year_month and end_year_month parameters.
        '''
        result = filter_databases(
            databases=self.databases,
            start_year_month='2024_02',
            end_year_month='2024_03'
        )
        expected = [
            'daemon__Sensor1__2024_02_10',
            'daemon__Sensor2__2024_02_20',
            'daemon__Sensor1__2024_03_05',
            'daemon__Sensor2__2024_03_15'
        ]
        self.assertEqual(result, natsorted(set(expected)))

    def test_filter_by_start_year_month_day(self):
        '''
        Test filtering databases by start_year_month_day parameter.
        '''
        result = filter_databases(
            databases=self.databases,
            start_year_month_day='2024_02_15'
        )
        expected = [
            'daemon__Sensor2__2024_02_20',
            'daemon__Sensor1__2024_03_05',
            'daemon__Sensor2__2024_03_15',
            'daemon__Sensor1__2024_04_01'
        ]
        self.assertEqual(result, natsorted(set(expected)))

    def test_filter_by_end_year_month_day(self):
        '''
        Test filtering databases by end_year_month_day parameter.
        '''
        result = filter_databases(
            databases=self.databases,
            end_year_month_day='2024_02_15'
        )
        expected = [
            'daemon__Sensor1__2024_01_15',
            'daemon__Sensor1__2024_02_10'
        ]
        self.assertEqual(result, natsorted(set(expected)))

    def test_filter_by_date_range_year_month_day(self):
        '''
        Test filtering databases by both start_year_month_day and end_year_month_day parameters.
        '''
        result = filter_databases(
            databases=self.databases,
            start_year_month_day='2024_02_01',
            end_year_month_day='2024_03_10'
        )

        expected = [
            'daemon__Sensor1__2024_02_10',
            'daemon__Sensor2__2024_02_20',
            'daemon__Sensor1__2024_03_05'
        ]
        self.assertEqual(result, natsorted(set(expected)))

    def test_non_dated_databases_excluded(self):
        '''
        Test that non-dated databases are excluded from date filtering.
        '''
        result = filter_databases(
            databases=self.databases,
            year_months=[
                '2024_01',
                '2024_02',
                '2024_03',
                '2024_04'
            ]
        )

        # Non-dated databases should be excluded
        self.assertNotIn('geolocation', result)
        self.assertNotIn('malicious', result)

    def test_empty_inputs(self):
        '''
        Test that the function handles empty inputs correctly.
        '''
        # Empty databases list
        result = filter_databases(
            databases=[],
            year_months=['2024_01']
        )
        self.assertEqual(result, [])

        # No filter criteria
        result = filter_databases(
            databases=self.databases
        )
        # Should return all databases, sorted
        self.assertEqual(result, natsorted(set(self.databases)))

    def test_no_matching_databases(self):
        '''
        Test when no databases match the filter criteria.
        '''
        result = filter_databases(
            databases=self.databases,
            year_months=['2022_12']
        )
        self.assertEqual(result, [])

class TestReverseDateRange(TestCase):
    '''
    Tests for the reverse_date_range function.

    This test suite verifies that the function correctly reverses the order of dates
    in a given date range.
    '''

    def test_empty_list(self):
        '''
        Test that an empty list returns an empty list.
        '''
        result = reverse_date_range([])
        self.assertEqual(result, [])

    def test_single_date(self):
        '''
        Test that a list with a single date returns the same list.
        '''
        result = reverse_date_range(['2023-01-01'])
        self.assertEqual(result, ['2023-01-01'])

    def test_multiple_dates(self):
        '''
        Test that a list with multiple dates returns the dates in reverse order.
        '''
        date_range = ['2023-01-01', '2023-01-02', '2023-01-03']
        result = reverse_date_range(date_range)
        expected = ['2023-01-03', '2023-01-02', '2023-01-01']
        self.assertEqual(result, expected)

    def test_already_reversed_dates(self):
        '''
        Test that a list with dates in reverse order returns the dates in forward order.
        '''
        date_range = ['2023-01-03', '2023-01-02', '2023-01-01']
        result = reverse_date_range(date_range)
        expected = ['2023-01-01', '2023-01-02', '2023-01-03']
        self.assertEqual(result, expected)

    def test_non_sequential_dates(self):
        '''
        Test that a list with non-sequential dates returns the dates in reverse order.
        '''
        date_range = ['2023-01-01', '2023-01-15', '2023-01-30']
        result = reverse_date_range(date_range)
        expected = ['2023-01-30', '2023-01-15', '2023-01-01']
        self.assertEqual(result, expected)

class TestRemoveTrailingSlash(TestCase):
    '''
    Tests for the remove_trailing_slash function.

    This test suite verifies that the function correctly removes trailing slashes
    from strings while leaving other characters intact.
    '''

    def test_string_with_trailing_slash(self):
        '''
        Test that a string with a trailing slash has it removed.
        '''
        result = remove_trailing_slash('example.com/')
        self.assertEqual(result, 'example.com')

    def test_string_with_multiple_trailing_slashes(self):
        '''
        Test that a string with multiple trailing slashes has all of them removed.
        '''
        result = remove_trailing_slash('example.com///')
        self.assertEqual(result, 'example.com')

    def test_string_without_trailing_slash(self):
        '''
        Test that a string without a trailing slash remains unchanged.
        '''
        result = remove_trailing_slash('example.com')
        self.assertEqual(result, 'example.com')

    def test_string_with_slash_in_middle(self):
        '''
        Test that a string with a slash in the middle but not at the end remains unchanged.
        '''
        result = remove_trailing_slash('example/path/file')
        self.assertEqual(result, 'example/path/file')

    def test_empty_string(self):
        '''
        Test that an empty string returns an empty string.
        '''
        result = remove_trailing_slash('')
        self.assertEqual(result, '')

class TestRemoveIdColumn(TestCase):
    '''
    Tests for the remove_id_column function.

    This test suite verifies that the function correctly removes the first element
    from a list or tuple, typically used to remove the 'ID' column from database rows.
    '''

    def test_remove_id_from_list(self):
        '''
        Test that the function correctly removes the first element from a list.
        '''
        input_list = ['ID', 'Date', 'Time', 'Event']
        result = remove_id_column(input_list)
        self.assertEqual(result, ['Date', 'Time', 'Event'])

    def test_remove_id_from_tuple(self):
        '''
        Test that the function correctly removes the first element from a tuple.
        '''
        input_tuple = (12, '2024-07-01', '00:00:36', '(daemon/notice)')
        result = remove_id_column(input_tuple)
        self.assertEqual(result, ('2024-07-01', '00:00:36', '(daemon/notice)'))

    def test_single_element_list(self):
        '''
        Test that the function returns an empty list when given a single-element list.
        '''
        input_list = ['ID']
        result = remove_id_column(input_list)
        self.assertEqual(result, [])

    def test_single_element_tuple(self):
        '''
        Test that the function returns an empty tuple when given a single-element tuple.
        '''
        input_tuple = (42,)
        result = remove_id_column(input_tuple)
        self.assertEqual(result, ())

class TestDnsResolverIsPrivateIp(TestCase):
    '''
    Tests for the dns_resolver_is_private_ip function.

    This test suite verifies that the function correctly determines if the DNS resolver
    is using a private IP address by checking the /etc/resolv.conf file.
    '''

    @patch('base.utils.settings.DEBUG', False)
    @patch('builtins.open', new_callable=mock_open, read_data='nameserver ***********\nnameserver ********\n')
    @patch('base.utils.is_private', return_value=True)
    def test_private_ip_resolvers(self, mock_is_private, mock_file):
        '''
        Test that the function returns True when all nameservers are private IPs.
        '''
        result = dns_resolver_is_private_ip()
        self.assertTrue(result)
        mock_is_private.assert_called()

    @patch('base.utils.settings.DEBUG', False)
    @patch('builtins.open', new_callable=mock_open, read_data='nameserver *******\nnameserver ***********\n')
    @patch('base.utils.is_private', side_effect=[False, True])
    def test_mixed_resolvers_returns_false(self, mock_is_private, mock_file):
        '''
        Test that the function returns False when at least one nameserver is a public IP.
        '''
        result = dns_resolver_is_private_ip()
        self.assertFalse(result)
        mock_is_private.assert_called()

    @patch('base.utils.settings.DEBUG', False)
    @patch('builtins.open', new_callable=mock_open, read_data='# This is a comment\nnameserver ***********\n')
    @patch('base.utils.is_private', return_value=True)
    def test_with_comments_in_file(self, mock_is_private, mock_file):
        '''
        Test that the function correctly ignores comment lines in the resolv.conf file.
        '''
        result = dns_resolver_is_private_ip()
        self.assertTrue(result)
        mock_is_private.assert_called_once_with('***********')

    @patch('base.utils.settings.DEBUG', False)
    @patch('builtins.open', new_callable=mock_open, read_data='search example.com\noptions ndots:1\nnameserver ***********\n')
    @patch('base.utils.is_private', return_value=True)
    def test_with_other_directives(self, mock_is_private, mock_file):
        '''
        Test that the function correctly handles other directives in the resolv.conf file.
        '''
        result = dns_resolver_is_private_ip()
        self.assertTrue(result)
        mock_is_private.assert_called_once_with('***********')

    @patch('base.utils.settings.DEBUG', True)
    def test_debug_mode_returns_true(self):
        '''
        Test that the function returns True when in debug mode without checking the file.
        '''
        result = dns_resolver_is_private_ip()
        self.assertTrue(result)

class TestCommandInstanceIsRunning(TestCase):
    '''
    Tests for the command_instance_is_running function.

    This test suite verifies that the function correctly determines if multiple
    instances of a Django custom command are running.
    '''

    @patch('base.utils.run')
    def test_single_instance_running(self, mock_run):
        '''
        Test that the function returns False when only one instance is running.
        '''
        # Mock the subprocess.run result for a single PID
        mock_process = MagicMock()
        mock_process.stdout = '123456\n'
        mock_run.return_value = mock_process

        result = command_instance_is_running('test_command')

        self.assertFalse(result)
        mock_run.assert_called_once_with(
            f'{BINARY_PATHS.pgrep} -f "test_command"',
            shell=True,
            universal_newlines=True,
            capture_output=True,
        )

    @patch('base.utils.run')
    def test_multiple_instances_running(self, mock_run):
        '''
        Test that the function returns True when multiple instances are running.
        '''
        # Mock the subprocess.run result for multiple PIDs
        mock_process = MagicMock()
        mock_process.stdout = '123456\n789012\n'
        mock_run.return_value = mock_process

        result = command_instance_is_running('test_command')

        self.assertTrue(result)
        mock_run.assert_called_once_with(
            f'{BINARY_PATHS.pgrep} -f "test_command"',
            shell=True,
            universal_newlines=True,
            capture_output=True,
        )

    @patch('base.utils.run')
    def test_empty_output(self, mock_run):
        '''
        Test that the function handles empty output correctly.
        '''
        # Mock the subprocess.run result for no PIDs (shouldn't happen in practice)
        mock_process = MagicMock()
        mock_process.stdout = ''
        mock_run.return_value = mock_process

        result = command_instance_is_running('test_command')

        self.assertFalse(result)
        mock_run.assert_called_once()

class TestServiceIsRunning(TestCase):
    '''
    Tests for the service_is_running function.

    This test suite verifies that the function correctly determines whether
    a service is running based on the return code of the service status command.
    '''

    @patch('base.utils.settings')
    @patch('base.utils.run')
    def test_debug_mode(self, mock_run, mock_settings):
        '''
        Test that the function returns True when in debug mode without checking service.
        '''
        mock_settings.DEBUG = True

        result = service_is_running('redis')

        self.assertTrue(result)
        mock_run.assert_not_called()

    @patch('base.utils.settings')
    @patch('base.utils.run')
    def test_service_running(self, mock_run, mock_settings):
        '''
        Test that the function returns True when the service is running.
        '''
        mock_settings.DEBUG = False
        mock_process = MagicMock()
        mock_process.returncode = 0
        mock_run.return_value = mock_process

        result = service_is_running('redis')

        self.assertTrue(result)
        mock_run.assert_called_once()

    @patch('base.utils.settings')
    @patch('base.utils.run')
    def test_service_not_running(self, mock_run, mock_settings):
        '''
        Test that the function returns False when the service is not running.
        '''
        mock_settings.DEBUG = False
        mock_process = MagicMock()
        mock_process.returncode = 1
        mock_run.return_value = mock_process

        result = service_is_running('redis')

        self.assertFalse(result)
        mock_run.assert_called_once()

class TestHmsToHourkey(TestCase):
    '''
    Tests for the hms_to_hourkey function.

    This test suite verifies that the function correctly converts a time string
    in HH:MM:SS format to an hour key string in the format 'HH:00 - HH:59'.
    '''

    def test_standard_time_conversion(self):
        '''
        Test that standard time values are correctly converted to hour key format.
        '''
        result = hms_to_hourkey('12:34:56')
        self.assertEqual(result, '12:00 - 12:59')

        result = hms_to_hourkey('09:15:30')
        self.assertEqual(result, '09:00 - 09:59')

    def test_boundary_times(self):
        '''
        Test that boundary time values (start and end of day) are correctly converted.
        '''
        # Start of day
        result = hms_to_hourkey('00:00:00')
        self.assertEqual(result, '00:00 - 00:59')

        # End of day
        result = hms_to_hourkey('23:59:59')
        self.assertEqual(result, '23:00 - 23:59')

    def test_different_minutes_seconds(self):
        '''
        Test that times with different minutes and seconds but same hour return the same hour key.
        '''
        result1 = hms_to_hourkey('15:00:00')
        result2 = hms_to_hourkey('15:30:45')
        result3 = hms_to_hourkey('15:59:59')

        self.assertEqual(result1, '15:00 - 15:59')
        self.assertEqual(result2, '15:00 - 15:59')
        self.assertEqual(result3, '15:00 - 15:59')
        self.assertEqual(result1, result2)
        self.assertEqual(result2, result3)

class TestCreateWarningMessage(TestCase):
    '''
    Tests for the create_warning_message function.

    This test suite verifies that the function correctly formats warning messages
    based on the provided parameters and user permissions.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.request = MagicMock(spec=HttpRequest)
        self.app_title = 'Test App'
        self.ymd = '2023-01-15'
        self.name_of_function = 'test_function'
        self.exception = ValueError('Test error message')

    def test_basic_message_for_regular_user(self):
        '''
        Test that a basic message is created for a regular user.
        '''
        self.request.user = MagicMock()
        self.request.user.is_superuser = False

        result = create_warning_message(
            request=self.request,
            app_title=self.app_title,
            ymd=self.ymd,
            name_of_function=self.name_of_function,
            exc=self.exception
        )

        expected = 'Test App: 2023-01-15'
        self.assertEqual(result, expected)

    def test_verbose_message_for_superuser(self):
        '''
        Test that a verbose message with function name and exception details is created for a superuser.
        '''
        self.request.user = MagicMock()
        self.request.user.is_superuser = True

        result = create_warning_message(
            request=self.request,
            app_title=self.app_title,
            ymd=self.ymd,
            name_of_function=self.name_of_function,
            exc=self.exception
        )

        expected = "Test App: 2023-01-15 -- test_function: ValueError('Test error message')"
        self.assertEqual(result, expected)

    def test_with_different_exception_types(self):
        '''
        Test that the function handles different exception types correctly.
        '''
        self.request.user = MagicMock()
        self.request.user.is_superuser = True

        # Test with TypeError
        type_error = TypeError('Type error test')
        result = create_warning_message(
            request=self.request,
            app_title=self.app_title,
            ymd=self.ymd,
            name_of_function=self.name_of_function,
            exc=type_error
        )
        expected = "Test App: 2023-01-15 -- test_function: TypeError('Type error test')"
        self.assertEqual(result, expected)

        # Test with KeyError
        key_error = KeyError('missing_key')
        result = create_warning_message(
            request=self.request,
            app_title=self.app_title,
            ymd=self.ymd,
            name_of_function=self.name_of_function,
            exc=key_error
        )
        expected = "Test App: 2023-01-15 -- test_function: KeyError('missing_key')"
        self.assertEqual(result, expected)

class TestReplaceYmdhmsAtBeginningOfLine(TestCase):
    '''
    Tests for the replace_ymdhms_at_beginning_of_line function.

    This test suite verifies that the function correctly replaces date and time
    at the beginning of a line with a new date and time string.
    '''

    def test_standard_replacement(self):
        '''
        Test that the function correctly replaces a standard date and time format.
        '''
        line = '2023-10-01 12:34:56 Some log message'
        new_ymdhms = '2023-11-01 13:45:00'
        result = replace_ymdhms_at_beginning_of_line(line, new_ymdhms)
        self.assertEqual(result, '2023-11-01 13:45:00 Some log message')

    def test_no_date_time_at_beginning(self):
        '''
        Test that the function returns the original line when no date and time pattern is found.
        '''
        line = 'No date and time here'
        new_ymdhms = '2023-11-01 13:45:00'
        result = replace_ymdhms_at_beginning_of_line(line, new_ymdhms)
        self.assertEqual(result, line)

    def test_different_date_formats(self):
        '''
        Test that the function works with different date formats that match the YMD_REGEX.
        '''
        line = '2023-01-05 00:00:01 Log entry with different date'
        new_ymdhms = '2023-12-31 23:59:59'
        result = replace_ymdhms_at_beginning_of_line(line, new_ymdhms)
        self.assertEqual(result, '2023-12-31 23:59:59 Log entry with different date')

    def test_with_special_characters(self):
        '''
        Test that the function correctly handles lines with special characters.
        '''
        line = '2023-10-01 12:34:56 [Error] Connection failed: timeout (***********:8080)'
        new_ymdhms = '2023-11-01 13:45:00'
        result = replace_ymdhms_at_beginning_of_line(line, new_ymdhms)
        self.assertEqual(result, '2023-11-01 13:45:00 [Error] Connection failed: timeout (***********:8080)')

    def test_partial_match(self):
        '''
        Test that the function doesn't replace when there's only a partial match.
        '''
        line = '2023-10-01Some log message'  # Missing space between date and time
        new_ymdhms = '2023-11-01 13:45:00'
        result = replace_ymdhms_at_beginning_of_line(line, new_ymdhms)
        self.assertEqual(result, line)

class TestVerboseTimeToMillisecond(TestCase):
    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.tuples = [
            ## verbose_time                 millisecond
            ('1 hour 5 minutes 20 seconds', 3920000),
            ('90 minutes',                  5400000),
            ('2 hours 15 seconds',          7215000),
            ('12 seconds',                  12000),
            ('5 minutes',                   300000),
            ('1 hour',                      3600000),
        ]

    def test_converting(self):
        for verbose_time, millisecond in self.tuples:
            self.assertEqual(verbose_time_to_millisecond(verbose_time), millisecond)

class TestEvenlySizedBatches(TestCase):
    '''
    Tests for the evenly_sized_batches function.

    This test suite verifies that the function correctly generates evenly sized batches
    from a given total length.
    '''

    def test_default_batch_size(self):
        '''
        Test that the function correctly generates batches with the default size of 10.
        '''
        batches = list(evenly_sized_batches(25))

        self.assertEqual(len(batches), 3)
        self.assertEqual(list(batches[0]), list(range(1, 11)))
        self.assertEqual(list(batches[1]), list(range(11, 21)))
        self.assertEqual(list(batches[2]), list(range(21, 26)))

    def test_custom_batch_size(self):
        '''
        Test that the function correctly generates batches with a custom size.
        '''
        batches = list(evenly_sized_batches(25, len_of_each_batch=7))

        self.assertEqual(len(batches), 4)
        self.assertEqual(list(batches[0]), list(range(1, 8)))
        self.assertEqual(list(batches[1]), list(range(8, 15)))
        self.assertEqual(list(batches[2]), list(range(15, 22)))
        self.assertEqual(list(batches[3]), list(range(22, 26)))

    def test_batch_size_larger_than_total(self):
        '''
        Test that the function correctly handles cases where batch size is larger than total length.
        '''
        batches = list(evenly_sized_batches(5, len_of_each_batch=10))

        self.assertEqual(len(batches), 1)
        self.assertEqual(list(batches[0]), list(range(1, 6)))

    def test_zero_total_length(self):
        '''
        Test that the function correctly handles a total length of zero.
        '''
        batches = list(evenly_sized_batches(0))

        self.assertEqual(len(batches), 0)

    def test_negative_total_length(self):
        '''
        Test that the function correctly handles a negative total length.
        '''
        batches = list(evenly_sized_batches(-5))

        self.assertEqual(len(batches), 0)

class TestIsInvalidLogDate(TestCase):
    '''
    Tests for the is_invalid_log_date function.

    This test suite verifies that the function correctly identifies invalid log dates
    based on comparison with the current date.
    '''

    def test_same_date_as_today(self):
        '''
        Test that a log date equal to today's date is considered invalid.
        '''
        today = '2023-05-15'
        log_date = '2023-05-15'

        result = is_invalid_log_date(log_date, today)

        self.assertTrue(result)

    def test_future_date(self):
        '''
        Test that a log date in the future is considered invalid.
        '''
        today = '2023-05-15'
        log_date = '2024-10-13'

        result = is_invalid_log_date(log_date, today)

        self.assertTrue(result)

    def test_past_date(self):
        '''
        Test that a log date in the past is considered valid.
        '''
        today = '2023-05-15'
        log_date = '2023-05-14'

        result = is_invalid_log_date(log_date, today)

        self.assertFalse(result)

        # Test with date further in the past
        log_date = '2022-12-31'

        result = is_invalid_log_date(log_date, today)

        self.assertFalse(result)

class TestHasNonPrintableBytes(TestCase):
    '''
    Tests for the has_non_printable_bytes function for logs lines.
    '''

    def setUp(self):
        ## __BY_AI__ lines generated by chatgpt
        self.invalid_lines = [
            '2021-02-23 03:54:31 WindowsServer-1 (user/notice) [MSWinEventLog	1	N/A	2605989	Mon] Jun 23 03:54:31 2025	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[dns] 6/23/2025 3:54:31 AM \x07\x08\x0b Control chars in log.',
            '2021-02-23 03:54:31 WindowsServer-1 (user/notice) [MSWinEventLog	1	N/A	2605989	Mon] Jun 23 03:54:31 2025	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[dns] 6/23/2025 3:54:31 AM \x08\x07i=8 PACKET UDP Rcv ******* cf00 Q [NOERROR] example.com',
            '2021-02-23 03:54:31 WindowsServer-1 (user/notice) [MSWinEventLog	1	N/A	2605989	Mon] Jun 23 03:54:31 2025	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[dns] 6/23/2025 3:54:31 AM Warning: Unexpected character \x1b in payload received from ***********',
            '2021-02-23 03:54:31 WindowsServer-1 (user/notice) [MSWinEventLog	1	N/A	2605989	Mon] Jun 23 03:54:31 2025	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[dns] 6/23/2025 3:54:31 AM System alert:\x0cCPU threshold exceeded\x0e. Immediate action required.',
        ]

    def test_lines_have_non_printable_bytes(self):
        '''
        Test if lines have non-printable bytes.
        '''
        for ln in self.invalid_lines:
            self.assertTrue(has_non_printable_bytes(ln))

class TestIsInvalidLn(TestCase):
    '''
    Tests for the _is_invalid_ln function for logs lines.
    '''

    def setUp(self):
        self.invalid_lines = [
            ## not ln
            '',

            ## 'ERROR name exceeds safe print buffer length' in ln
            '2025-06-21 07:10:12 MSYADDSI (user/notice) [MSWinEventLog       1       N/A     98031276        Sat] Jun 21 07:10:12 2025       N/A     N/A     N/A   N/A      N/A     N/A     N/A             [dns] 6/21/2025 7:10:12 AM 0C20 PACKET  00000294AED611C0 UDP Rcv 192.168.11.29   08a1   Q [0001   D   NOERROR]        (1)h(1)l(1)l(1)l(1-- [ERROR name exceeds safe print buffer length]    N/A',

            ## 'ERROR length byte' in ln
            '2023-05-12 23:36:10 Object-1 (user/notice) [MSWinEventLog    1       N/A     136220     Thu] Jun 12 23:58:47 2025       N/A     N/A     N/A     N/A     N/AN/A     N/A             [dns] 1/12/2025 11:58:47 PM 0FAC PACKET  00000124085DBD40 UDP Rcv *******    4567   Q [0001   D   NOERROR]        (4)ted[ERROR length byte: 0x63]        N/A',

            ## 'leads outside message' in ln
            '2023-05-12 23:36:10 Object-1 (user/notice) [MSWinEventLog    1       N/A     135124     Thu] Jun 12 23:22:18 2025       N/A     N/A     N/A     N/A     N/AN/A     N/A             [dns] 1/12/2025 11:22:18 PM 0FA4 PACKET  000001247B3794E0 UDP Rcv *******    4567   Q [0001   D   NOERROR]        (2)1x(49)[ERROR length byte: 0x31 at 000001247B37AE30 leads outside message]   N/A',

            ## 'Exiting on signal' in ln
            '2023-05-12 23:36:10 Object-1 (auth/info) [sshguard] Exiting on signal.',

            ## 'Now monitoring attacks' in ln
            '2023-05-12 23:36:10 Object-1 (auth/info) [sshguard] Now monitoring attacks.',

            ## 'spp_arpspoof' in ln
            '2023-05-12 23:36:10 Object-1 (auth/alert) [snort] (spp_arpspoof) Unicast ARP request',

            ## 'because it is a directory, not a file' in ln
            "FATAL: Cannot open '/var/squid/logs' because it is a directory, not a file.",
            ## __TODO__ check if this is what real log line looks like

            ## not ln[0].isdigit()
            'MIICGDCCAQACAQEwDQYJKoZIhvcNAQELBQAwgZsxCzAJBgNVBAMMAkNBMRcwFQYK',
        ]

    def test_lines_are_invalid(self):
        '''
        Test if lines are considered invalid.
        '''
        for ln in self.invalid_lines:
            self.assertTrue(_is_invalid_ln(ln))

class TestParseLnDaemon(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.sensor_list_of_names_and_addresses = sensor_list_of_names_and_addresses
        self.sensor_dict_of_addresses_and_names = sensor_dict_of_addresses_and_names

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        for e_t in DaemonConfig.EVENT_TYPES.value:
            ln = f'2023-05-12 23:36:10 Sensor-1 {e_t} [charon] 05[IKE] <con2|204> activating new tasks'

            object_name, parsed_ln = parse_ln(
                ln,
                DaemonConfig,
                self.sensor_list_of_names_and_addresses,
                self.sensor_dict_of_addresses_and_names
            )

            self.assertEqual(object_name, 'Sensor-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], e_t)
            self.assertEqual(parsed_ln[3], '[charon]')
            self.assertEqual(parsed_ln[4], '05[IKE] <con2|204> activating new tasks')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        for e_t in DaemonConfig.EVENT_TYPES.value:
            ln = f'2023-05-12 23:36:10 *********** {e_t} [charon] 05[IKE] <con2|204> activating new tasks'

            object_name, parsed_ln = parse_ln(
                ln,
                DaemonConfig,
                self.sensor_list_of_names_and_addresses,
                self.sensor_dict_of_addresses_and_names
            )

            self.assertEqual(object_name, 'Sensor-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], e_t)
            self.assertEqual(parsed_ln[3], '[charon]')
            self.assertEqual(parsed_ln[4], '05[IKE] <con2|204> activating new tasks')

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = f'2023-05-12 23:36:10 Invalid-Sensor {DaemonConfig.EVENT_TYPES.value[0]} [charon] 05[IKE] <con2|204> activating new tasks'

        object_name, parsed_ln = parse_ln(
            ln,
            DaemonConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        ln = '2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [charon] 05[IKE] <con2|204> activating new tasks'

        object_name, parsed_ln = parse_ln(
            ln,
            DaemonConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        pass

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            DaemonConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnDHCP(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        pass

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        for e_t in DHCPConfig.EVENT_TYPES.value:
            ln = f'2024-12-08 20:01:30 WindowsServer-1 {e_t} [MSWinEventLog	1	N/A	229692	Sun] Dec 08 20:01:30 2024	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[dhcp] 11,12/08/24,20:01:29,Renew,***********,xPhone.sth.local,38A4EDBA48B9,,640399471,0,,,,0x616E64726F69642D646863702D382E302E30,android-dhcp-8.0.0,,,,0\tN/A'

            object_name, parsed_ln = parse_ln(
                ln,
                DHCPConfig,
                None,
                None,
            )

            self.assertEqual(object_name, None)

            self.assertEqual(parsed_ln[0], normalize_date('12/08/24'))
            self.assertEqual(parsed_ln[1], '20:01:29')
            self.assertEqual(parsed_ln[2], '11')
            self.assertEqual(parsed_ln[3], 'Renew')
            self.assertEqual(parsed_ln[4], '***********')
            self.assertEqual(parsed_ln[5], 'xPhone.sth.local')
            self.assertEqual(parsed_ln[6], '38A4EDBA48B9')
            self.assertEqual(parsed_ln[7], '')
            self.assertEqual(parsed_ln[8], '640399471')
            self.assertEqual(parsed_ln[9], '0')
            self.assertEqual(parsed_ln[10], '')
            self.assertEqual(parsed_ln[11], '')
            self.assertEqual(parsed_ln[12], '')
            self.assertEqual(parsed_ln[13], '0x616E64726F69642D646863702D382E302E30')
            self.assertEqual(parsed_ln[14], 'android-dhcp-8.0.0')
            self.assertEqual(parsed_ln[15], '')
            self.assertEqual(parsed_ln[16], '')
            self.assertEqual(parsed_ln[17], '')
            self.assertEqual(parsed_ln[18], '0')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        pass

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        pass

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        ln = '2024-12-08 20:01:30 WindowsServer-1 (invalid-event-type) [MSWinEventLog	1	N/A	229692	Sun] Dec 08 20:01:30 2024	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[dhcp] 11,12/08/24,20:01:29,Renew,***********,xPhone.sth.local,38A4EDBA48B9,,640399471,0,,,,0x616E64726F69642D646863702D382E302E30,android-dhcp-8.0.0,,,,0\tN/A'

        object_name, parsed_ln = parse_ln(
            ln,
            DHCPConfig,
            None,
            None,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        ln = f'2024-12-08 20:01:30 WindowsServer-1 {DHCPConfig.EVENT_TYPES.value[0]} [MSWinEventLog	1	N/A	229692	Sun] Dec 08 20:01:30 2024	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[invalid-alert-type] 11,12/08/24,20:01:29,Renew,***********,xPhone.sth.local,38A4EDBA48B9,,640399471,0,,,,0x616E64726F69642D646863702D382E302E30,android-dhcp-8.0.0,,,,0\tN/A'

        object_name, parsed_ln = parse_ln(
            ln,
            DHCPConfig,
            None,
            None,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        for ln in [
            '2023-05-12 23:36:10 This is an invalid line',

            ## ln has: 'ID,Date,Time,Description,IP Address,Host Name,MAC Address,User Name'
            f'2023-05-12 23:36:10 WindowsServer-1 {DHCPConfig.EVENT_TYPES.value[0]} [MSWinEventLog       1       N/A     152263  Thu] Jul 10 00:00:47 2025       N/A     N/A     N/A     N/A     N/A     N/A     N/A             [dhcp] ID,Date,Time,Description,IP Address,Host Name,MAC Address,User Name, TransactionID, QResult,Probationtime, CorrelationID,Dhcid,VendorClass(Hex),VendorClass(ASCII),UserClass(Hex),UserClass(ASCII),RelayAgentInformation,DnsRegError. N/A',
        ]:
            object_name, parsed_ln = parse_ln(
                ln,
                DHCPConfig,
                None,
                None,
            )

            self.assertEqual(object_name, None)
            self.assertEqual(parsed_ln, None)

class TestParseLnDNS(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        pass

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        for e_t in DNSConfig.EVENT_TYPES.value:
            ln = f'2024-12-08 01:16:44 WindowsServer-1 {e_t} [MSWinEventLog     1       N/A     1089080 Sun] Dec 08 01:16:44 2024       N/A     N/A     N/A     N/A   N/A      N/A     N/A             [dns] 12/8/2024 1:16:44 AM 0B54 PACKET  000001D4F30A0C90 UDP Snd *************   e942   Q [0001   D   NOERROR] AAAA   (9)speedtest(10)example(2)AaBbCc(2)id(0)\tN/A'

            object_name, parsed_ln = parse_ln(
                ln,
                DNSConfig,
                None,
                None,
            )

            self.assertEqual(object_name, None)

            self.assertEqual(parsed_ln[0],  normalize_date('12/8/2024'))
            self.assertEqual(parsed_ln[1],  normalize_time('1:16:44 AM'))
            self.assertEqual(parsed_ln[2],  '0B54')
            self.assertEqual(parsed_ln[3],  'PACKET')
            self.assertEqual(parsed_ln[4],  '000001D4F30A0C90')
            self.assertEqual(parsed_ln[5],  'UDP')
            self.assertEqual(parsed_ln[6],  'Snd')
            self.assertEqual(parsed_ln[7],  '*************')
            self.assertEqual(parsed_ln[8],  'e942')
            self.assertEqual(parsed_ln[9],  '')
            self.assertEqual(parsed_ln[10], 'Q')
            self.assertEqual(parsed_ln[11], '0001')
            self.assertEqual(parsed_ln[12], 'D')
            self.assertEqual(parsed_ln[13], 'NOERROR')
            self.assertEqual(parsed_ln[14], 'AAAA')
            self.assertEqual(parsed_ln[15], normalize_dns_question_name('(9)speedtest(10)example(2)AaBbCc(2)id(0)').lower())

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        pass

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        pass

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        ln = '2024-12-08 01:16:44 WindowsServer-1 (invalid-event-type) [MSWinEventLog     1       N/A     1089080 Sun] Dec 08 01:16:44 2024       N/A     N/A     N/A     N/A   N/A      N/A     N/A             [dns] 12/8/2024 1:16:44 AM 0B54 PACKET  000001D4F30A0C90 UDP Snd *************   e942   Q [0001   D   NOERROR] AAAA   (9)speedtest(10)example(2)AaBbCc(2)id(0)\tN/A'

        object_name, parsed_ln = parse_ln(
            ln,
            DNSConfig,
            None,
            None,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        ln = f'2024-12-08 01:16:44 WindowsServer-1 {DNSConfig.EVENT_TYPES.value[0]} [MSWinEventLog     1       N/A     1089080 Sun] Dec 08 01:16:44 2024       N/A     N/A     N/A     N/A   N/A      N/A     N/A             [invalid-alert-type] 12/8/2024 1:16:44 AM 0B54 PACKET  000001D4F30A0C90 UDP Snd *************   e942   Q [0001   D   NOERROR] AAAA   (9)speedtest(10)example(2)AaBbCc(2)id(0)\tN/A'

        object_name, parsed_ln = parse_ln(
            ln,
            DNSConfig,
            None,
            None,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            DNSConfig,
            None,
            None,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnFilterLog(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.sensor_list_of_names_and_addresses = sensor_list_of_names_and_addresses
        self.sensor_dict_of_addresses_and_names = sensor_dict_of_addresses_and_names

    def test_valid_line_ipv4_tcp(self):
        '''
        Test parsing a valid log line with IPv4 TCP protocol.
        '''
        ln = '2023-05-12 23:36:10 Sensor-1 (local0/info) [filterlog] 85,,,1000006862,pppoe0,match,pass,out,4,0x0,,64,12345,0,DF,6,tcp,60,*************,*******,12345,443,40,S,12345,0,65535,,,,'

        object_name, parsed_ln = parse_ln(
            ln,
            FilterLogConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, 'Sensor-1')

        self.assertEqual(parsed_ln[0],  '2023-05-12')
        self.assertEqual(parsed_ln[1],  '23:36:10')
        self.assertEqual(parsed_ln[2],  'tcp')
        self.assertEqual(parsed_ln[3],  '*************')
        self.assertEqual(parsed_ln[4],  '*******')
        self.assertEqual(parsed_ln[5],  '12345')
        self.assertEqual(parsed_ln[6],  '443')
        self.assertEqual(parsed_ln[7],  '1000006862')
        self.assertEqual(parsed_ln[8],  'pppoe0')
        self.assertEqual(parsed_ln[9],  'match')
        self.assertEqual(parsed_ln[10], 'pass')
        self.assertEqual(parsed_ln[11], 'out')

    def test_valid_line_ipv6_udp(self):
        '''
        Test parsing a valid log line with IPv6 UDP protocol.
        '''
        ln = '2023-05-12 23:36:10 Sensor-1 (local0/info) [filterlog] 85,,,1000006862,pppoe0,match,pass,out,6,0x0,0,64,udp,17,80,2001:db8::1,2001:db8::2,12345,53,40'

        object_name, parse_line = parse_ln(
            ln,
            FilterLogConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, 'Sensor-1')

        self.assertEqual(parse_line[0],  '2023-05-12')
        self.assertEqual(parse_line[1],  '23:36:10')
        self.assertEqual(parse_line[2],  'udp')
        self.assertEqual(parse_line[3],  '2001:db8::1')
        self.assertEqual(parse_line[4],  '2001:db8::2')
        self.assertEqual(parse_line[5],  '12345')
        self.assertEqual(parse_line[6],  '53')
        self.assertEqual(parse_line[7],  '1000006862')
        self.assertEqual(parse_line[8],  'pppoe0')
        self.assertEqual(parse_line[9],  'match')
        self.assertEqual(parse_line[10], 'pass')
        self.assertEqual(parse_line[11], 'out')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        ln = '2023-05-12 23:36:10 *********** (local0/info) [filterlog] 85,,,1000006862,pppoe0,match,pass,out,4,0x0,,64,12345,0,DF,6,tcp,60,*************,*******,12345,443,40,S,12345,0,65535,,,,'

        object_name, parsed_ln = parse_ln(
            ln,
            FilterLogConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, 'Sensor-1')

        self.assertEqual(parsed_ln[0],  '2023-05-12')
        self.assertEqual(parsed_ln[1],  '23:36:10')
        self.assertEqual(parsed_ln[2],  'tcp')
        self.assertEqual(parsed_ln[3],  '*************')
        self.assertEqual(parsed_ln[4],  '*******')
        self.assertEqual(parsed_ln[5],  '12345')
        self.assertEqual(parsed_ln[6],  '443')
        self.assertEqual(parsed_ln[7],  '1000006862')
        self.assertEqual(parsed_ln[8],  'pppoe0')
        self.assertEqual(parsed_ln[9],  'match')
        self.assertEqual(parsed_ln[10], 'pass')
        self.assertEqual(parsed_ln[11], 'out')

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = '2023-05-12 23:36:10 Invalid-Sensor (local0/info) [filterlog] 85,,,1000006862,pppoe0,match,pass,out,4,0x0,,64,12345,0,DF,6,tcp,60,*************,*******,12345,443,40,S,12345,0,65535,,,,'

        object_name, parsed_ln = parse_ln(
            ln,
            FilterLogConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        pass

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        ln = '2023-05-12 23:36:10 Sensor-1 (local0/info) [invalid-alert-type] 85,,,1000006862,pppoe0,match,pass,out,4,0x0,,64,12345,0,DF,6,tcp,60,*************,*******,12345,443,40,S,12345,0,65535,,,,'

        object_name, parsed_ln = parse_ln(
            ln,
            FilterLogConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            FilterLogConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnRouter(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.router_list_of_names_and_addresses = router_list_of_names_and_addresses
        self.router_dict_of_addresses_and_names = router_dict_of_addresses_and_names

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        for e_t in RouterConfig.EVENT_TYPES.value:
            ln = f'2023-05-12 23:36:10 Router-1 {e_t} [%LINK-3-UPDOWN] Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown'

            object_name, parsed_ln = parse_ln(
                ln,
                RouterConfig,
                self.router_list_of_names_and_addresses,
                self.router_dict_of_addresses_and_names,
            )

            self.assertEqual(object_name, 'Router-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], e_t)
            self.assertEqual(parsed_ln[3], '[%LINK-3-UPDOWN]')
            self.assertEqual(parsed_ln[4], 'Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        for e_t in RouterConfig.EVENT_TYPES.value:
            ln = f'2023-05-12 23:36:10 *********** {e_t} [%LINK-3-UPDOWN] Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown'

            object_name, parsed_ln = parse_ln(
                ln,
                RouterConfig,
                self.router_list_of_names_and_addresses,
                self.router_dict_of_addresses_and_names,
            )

            self.assertEqual(object_name, 'Router-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], e_t)
            self.assertEqual(parsed_ln[3], '[%LINK-3-UPDOWN]')
            self.assertEqual(parsed_ln[4], 'Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown')

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = f'2023-05-12 23:36:10 Invalid-Router {RouterConfig.EVENT_TYPES.value[0]} [%LINK-3-UPDOWN] Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown'

        object_name, parsed_ln = parse_ln(
            ln,
            RouterConfig,
            self.router_list_of_names_and_addresses,
            self.router_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        ln = '2023-05-12 23:36:10 Router-1 (invalid-event-type) [%LINK-3-UPDOWN] Interface Foreign Exchange Office 0/0/0, changed state to Administrative Shutdown'

        object_name, parsed_ln = parse_ln(
            ln,
            RouterConfig,
            self.router_list_of_names_and_addresses,
            self.router_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        pass

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            RouterConfig,
            self.router_list_of_names_and_addresses,
            self.router_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnRouterBoard(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.routerboard_list_of_names_and_addresses = routerboard_list_of_names_and_addresses
        self.routerboard_dict_of_addresses_and_names = routerboard_dict_of_addresses_and_names

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        for e_t in RouterBoardConfig.EVENT_TYPES.value:
            ln = f'2023-05-12 23:36:10 RouterBoard-1 {e_t} [connection] established from *******, port: 37 to *******'

            object_name, parsed_ln = parse_ln(
                ln,
                RouterBoardConfig,
                self.routerboard_list_of_names_and_addresses,
                self.routerboard_dict_of_addresses_and_names,
            )

            self.assertEqual(object_name, 'RouterBoard-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], e_t)
            self.assertEqual(parsed_ln[3], '[connection]')
            self.assertEqual(parsed_ln[4], 'established from *******, port: 37 to *******')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        for e_t in RouterBoardConfig.EVENT_TYPES.value:
            ln = f'2023-05-12 23:36:10 *********** {e_t} [connection] established from *******, port: 37 to *******'

            object_name, parsed_ln = parse_ln(
                ln,
                RouterBoardConfig,
                self.routerboard_list_of_names_and_addresses,
                self.routerboard_dict_of_addresses_and_names,
            )

            self.assertEqual(object_name, 'RouterBoard-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], e_t)
            self.assertEqual(parsed_ln[3], '[connection]')
            self.assertEqual(parsed_ln[4], 'established from *******, port: 37 to *******')

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = f'2023-05-12 23:36:10 Invalid-RouterBoard {RouterBoardConfig.EVENT_TYPES.value[0]} [connection] established from *******, port: 37 to *******'

        object_name, parsed_ln = parse_ln(
            ln,
            RouterBoardConfig,
            self.routerboard_list_of_names_and_addresses,
            self.routerboard_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        ln = '2023-05-12 23:36:10 RouterBoard-1 (invalid-event-type) [connection] established from *******, port: 37 to *******'

        object_name, parsed_ln = parse_ln(
            ln,
            RouterBoardConfig,
            self.routerboard_list_of_names_and_addresses,
            self.routerboard_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        pass

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            RouterBoardConfig,
            self.routerboard_list_of_names_and_addresses,
            self.routerboard_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnSnort(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.sensor_list_of_names_and_addresses = sensor_list_of_names_and_addresses
        self.sensor_dict_of_addresses_and_names = sensor_dict_of_addresses_and_names

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        for e_t in SnortConfig.EVENT_TYPES.value:
            ln = '2023-05-12 23:36:10 Sensor-1 ' + e_t + ' [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {TCP} *******:94 -> *******:32'

            object_name, parsed_ln = parse_ln(
                ln,
                SnortConfig,
                self.sensor_list_of_names_and_addresses,
                self.sensor_dict_of_addresses_and_names
            )

            self.assertEqual(object_name, 'Sensor-1')

            self.assertEqual(parsed_ln[0],  '2023-05-12')
            self.assertEqual(parsed_ln[1],  '23:36:10')
            self.assertEqual(parsed_ln[2],  '1:1448:20')
            self.assertEqual(parsed_ln[3],  'POLICY-OTHER Microsoft')
            self.assertEqual(parsed_ln[4],  'Generic Command')
            self.assertEqual(parsed_ln[5],  '3')
            self.assertEqual(parsed_ln[6],  'TCP')
            self.assertEqual(parsed_ln[7],  '*******')
            self.assertEqual(parsed_ln[8],  '94')
            self.assertEqual(parsed_ln[9],  '*******')
            self.assertEqual(parsed_ln[10], '32')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        for e_t in SnortConfig.EVENT_TYPES.value:
            ln = '2023-05-12 23:36:10 *********** ' + e_t + ' [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {TCP} *******:94 -> *******:32'

            object_name, parsed_ln = parse_ln(
                ln,
                SnortConfig,
                self.sensor_list_of_names_and_addresses,
                self.sensor_dict_of_addresses_and_names
            )

            self.assertEqual(object_name, 'Sensor-1')

            self.assertEqual(parsed_ln[0],  '2023-05-12')
            self.assertEqual(parsed_ln[1],  '23:36:10')
            self.assertEqual(parsed_ln[2],  '1:1448:20')
            self.assertEqual(parsed_ln[3],  'POLICY-OTHER Microsoft')
            self.assertEqual(parsed_ln[4],  'Generic Command')
            self.assertEqual(parsed_ln[5],  '3')
            self.assertEqual(parsed_ln[6],  'TCP')
            self.assertEqual(parsed_ln[7],  '*******')
            self.assertEqual(parsed_ln[8],  '94')
            self.assertEqual(parsed_ln[9],  '*******')
            self.assertEqual(parsed_ln[10], '32')

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = '2023-05-12 23:36:10 Invalid-Sensor ' + SnortConfig.EVENT_TYPES.value[0] + ' [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {TCP} *******:94 -> *******:32'

        object_name, parsed_ln = parse_ln(
            ln,
            SnortConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        ln = '2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {TCP} *******:94 -> *******:32'

        object_name, parsed_ln = parse_ln(
            ln,
            SnortConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        ln = '2023-05-12 23:36:10 Sensor-1 ' + SnortConfig.EVENT_TYPES.value[0] + ' [invalid-alert-type] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {TCP} *******:94 -> *******:32'

        object_name, parsed_ln = parse_ln(
            ln,
            SnortConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            SnortConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnSquid(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.sensor_list_of_names_and_addresses = sensor_list_of_names_and_addresses
        self.sensor_dict_of_addresses_and_names = sensor_dict_of_addresses_and_names

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        ln = '2023-05-12 23:36:10 Sensor-1 (local4/info) [(squid-1)] 1729759769.387 0 *********** NONE_NONE/503 4101 GET https://example.org/AaBbCc - HIER_NONE/- text/html'

        object_name, parsed_ln = parse_ln(
            ln,
            SquidConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, 'Sensor-1')

        self.assertEqual(parsed_ln[0],  '2023-05-12')
        self.assertEqual(parsed_ln[1],  '23:36:10')
        self.assertEqual(parsed_ln[2],  '0')
        self.assertEqual(parsed_ln[3],  '***********')
        self.assertEqual(parsed_ln[4],  'NONE_NONE')
        self.assertEqual(parsed_ln[5],  '503')
        self.assertEqual(parsed_ln[6],  '4101')
        self.assertEqual(parsed_ln[7],  'GET')
        self.assertEqual(parsed_ln[8],  'https://example.org/AaBbCc'.lower())
        self.assertEqual(parsed_ln[9],  '-')
        self.assertEqual(parsed_ln[10], 'HIER_NONE')
        self.assertEqual(parsed_ln[11], '-')
        self.assertEqual(parsed_ln[12], 'text/html')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        ln = '2023-05-12 23:36:10 *********** (local4/info) [(squid-1)] 1729759769.387 0 *********** NONE_NONE/503 4101 GET https://example.org/AaBbCc - HIER_NONE/- text/html'

        object_name, parsed_ln = parse_ln(
            ln,
            SquidConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, 'Sensor-1')

        self.assertEqual(parsed_ln[0],  '2023-05-12')
        self.assertEqual(parsed_ln[1],  '23:36:10')
        self.assertEqual(parsed_ln[2],  '0')
        self.assertEqual(parsed_ln[3],  '***********')
        self.assertEqual(parsed_ln[4],  'NONE_NONE')
        self.assertEqual(parsed_ln[5],  '503')
        self.assertEqual(parsed_ln[6],  '4101')
        self.assertEqual(parsed_ln[7],  'GET')
        self.assertEqual(parsed_ln[8],  'https://example.org/AaBbCc'.lower())
        self.assertEqual(parsed_ln[9],  '-')
        self.assertEqual(parsed_ln[10], 'HIER_NONE')
        self.assertEqual(parsed_ln[11], '-')
        self.assertEqual(parsed_ln[12], 'text/html')

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = '2023-05-12 23:36:10 Invalid-Sensor (local4/info) [(squid-1)] 1729759769.387 0 *********** NONE_NONE/503 4101 GET https://example.org/AaBbCc - HIER_NONE/- text/html'

        object_name, parsed_ln = parse_ln(
            ln,
            SquidConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        pass

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        ln = '2023-05-12 23:36:10 Sensor-1 (local4/info) [invalid-alert-type] 1729759769.387 0 *********** NONE_NONE/503 4101 GET https://example.org/AaBbCc - HIER_NONE/- text/html'

        object_name, parsed_ln = parse_ln(
            ln,
            SquidConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            SquidConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnSwitch(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.switch_list_of_names_and_addresses = switch_list_of_names_and_addresses
        self.switch_dict_of_addresses_and_names = switch_dict_of_addresses_and_names

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        for e_t in SwitchConfig.EVENT_TYPES.value:
            ln = f'2023-05-12 23:36:10 Switch-1 {e_t} [%SFF8472-5-THRESHOLD_VIOLATION] Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)'

            object_name, parsed_ln = parse_ln(
                ln,
                SwitchConfig,
                self.switch_list_of_names_and_addresses,
                self.switch_dict_of_addresses_and_names,
            )

            self.assertEqual(object_name, 'Switch-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], e_t)
            self.assertEqual(parsed_ln[3], '[%SFF8472-5-THRESHOLD_VIOLATION]')
            self.assertEqual(parsed_ln[4], 'Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        for e_t in SwitchConfig.EVENT_TYPES.value:
            ln = f'2023-05-12 23:36:10 *********** {e_t} [%SFF8472-5-THRESHOLD_VIOLATION] Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)'

            object_name, parsed_ln = parse_ln(
                ln,
                SwitchConfig,
                self.switch_list_of_names_and_addresses,
                self.switch_dict_of_addresses_and_names,
            )

            self.assertEqual(object_name, 'Switch-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], e_t)
            self.assertEqual(parsed_ln[3], '[%SFF8472-5-THRESHOLD_VIOLATION]')
            self.assertEqual(parsed_ln[4], 'Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)')

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = f'2023-05-12 23:36:10 Invalid-Switch {SwitchConfig.EVENT_TYPES.value[0]} [%SFF8472-5-THRESHOLD_VIOLATION] Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)'

        object_name, parsed_ln = parse_ln(
            ln,
            SwitchConfig,
            self.switch_list_of_names_and_addresses,
            self.switch_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        ln = f'2023-05-12 23:36:10 Switch-1 (invalid-event-type) {SwitchConfig.EVENT_TYPES.value[0]} [%SFF8472-5-THRESHOLD_VIOLATION] Te2/1/1: Rx power low warning; Operating value: -14.8 dBm, Threshold value: -14.1 dBm. (Dis-Sw2-2)'

        object_name, parsed_ln = parse_ln(
            ln,
            SwitchConfig,
            self.switch_list_of_names_and_addresses,
            self.switch_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        pass

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            SwitchConfig,
            self.switch_list_of_names_and_addresses,
            self.switch_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnUserAudit(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.sensor_list_of_names_and_addresses = sensor_list_of_names_and_addresses
        self.sensor_dict_of_addresses_and_names = sensor_dict_of_addresses_and_names

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        for e_t in UserAuditConfig.EVENT_TYPES.value:
            ## JUMP_1
            for message in [
                "/index.php: Successful login for user 'admin' from: *********** (Local Database)",
                "/index.php: User logged out for user 'admin' from: *********** (Local Database)",
            ]:
                ln = f'2023-05-12 23:36:10 Sensor-1 {e_t} [php-fpm] {message}'

                object_name, parsed_ln = parse_ln(
                    ln,
                    UserAuditConfig,
                    self.sensor_list_of_names_and_addresses,
                    self.sensor_dict_of_addresses_and_names
                )

                self.assertEqual(object_name, 'Sensor-1')

                self.assertEqual(parsed_ln[0], '2023-05-12')
                self.assertEqual(parsed_ln[1], '23:36:10')
                self.assertEqual(parsed_ln[2], '[php-fpm]')
                self.assertEqual(parsed_ln[3], message)

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        for e_t in UserAuditConfig.EVENT_TYPES.value:
            for message in [
                "/index.php: Successful login for user 'admin' from: *********** (Local Database)",
                "/index.php: User logged out for user 'admin' from: *********** (Local Database)",
            ]:
                ln = f'2023-05-12 23:36:10 *********** {e_t} [php-fpm] {message}'

                object_name, parsed_ln = parse_ln(
                    ln,
                    UserAuditConfig,
                    self.sensor_list_of_names_and_addresses,
                    self.sensor_dict_of_addresses_and_names
                )

                self.assertEqual(object_name, 'Sensor-1')

                self.assertEqual(parsed_ln[0], '2023-05-12')
                self.assertEqual(parsed_ln[1], '23:36:10')
                self.assertEqual(parsed_ln[2], '[php-fpm]')
                self.assertEqual(parsed_ln[3], message)

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = f"2023-05-12 23:36:10 Invalid-Sensor {UserAuditConfig.EVENT_TYPES.value[0]} [php-fpm] /index.php: Successful login for user 'admin' from: *********** (Local Database)"

        object_name, parsed_ln = parse_ln(
            ln,
            UserAuditConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        ln = f"2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [php-fpm] /index.php: Successful login for user 'admin' from: *********** (Local Database)"

        object_name, parsed_ln = parse_ln(
            ln,
            UserAuditConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        pass

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        for ln in [
            '2023-05-12 23:36:10 This is an invalid line',

            ## 'Successful login' not in ln.
            ## this line is a copy of the valid line used in JUMP_1
            ## with one difference: 'Successful login' is removed
            ## intentionally turning it into an invalid line.
            f"2023-05-12 23:36:10 Sensor-1 {UserAuditConfig.EVENT_TYPES.value[0]} [php-fpm] /index.php: for user 'admin' from: *********** (Local Database)",

            ## 'User logged out' not in ln.
            ## this line is a copy of the valid line used in JUMP_1
            ## with one difference: 'User logged out' is removed
            ## intentionally turning it into an invalid line.
            f"2023-05-12 23:36:10 Sensor-1 {UserAuditConfig.EVENT_TYPES.value[0]} [php-fpm] /index.php: for user 'admin' from: *********** (Local Database)",
        ]:
            object_name, parsed_ln = parse_ln(
                ln,
                UserAuditConfig,
                self.sensor_list_of_names_and_addresses,
                self.sensor_dict_of_addresses_and_names
            )

            self.assertEqual(object_name, None)
            self.assertEqual(parsed_ln, None)

class TestParseLnUserNotice(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.sensor_list_of_names_and_addresses = sensor_list_of_names_and_addresses
        self.sensor_dict_of_addresses_and_names = sensor_dict_of_addresses_and_names

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        for e_t in UserNoticeConfig.EVENT_TYPES.value:
            ln = f"2023-05-12 23:36:10 Sensor-1 {e_t} [openvpn] openvpn server 'someserver' user 'someuser' address '*******:80' - connecting"

            object_name, parsed_ln = parse_ln(
                ln,
                UserNoticeConfig,
                self.sensor_list_of_names_and_addresses,
                self.sensor_dict_of_addresses_and_names
            )

            self.assertEqual(object_name, 'Sensor-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], 'someserver')
            self.assertEqual(parsed_ln[3], 'someuser')
            self.assertEqual(parsed_ln[4], '*******')
            self.assertEqual(parsed_ln[5], '80')
            self.assertEqual(parsed_ln[6], 'connecting')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        for e_t in UserNoticeConfig.EVENT_TYPES.value:
            ln = f"2023-05-12 23:36:10 *********** {e_t} [openvpn] openvpn server 'someserver' user 'someuser' address '*******:80' - connecting"

            object_name, parsed_ln = parse_ln(
                ln,
                UserNoticeConfig,
                self.sensor_list_of_names_and_addresses,
                self.sensor_dict_of_addresses_and_names
            )

            self.assertEqual(object_name, 'Sensor-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], 'someserver')
            self.assertEqual(parsed_ln[3], 'someuser')
            self.assertEqual(parsed_ln[4], '*******')
            self.assertEqual(parsed_ln[5], '80')
            self.assertEqual(parsed_ln[6], 'connecting')

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = f"2023-05-12 23:36:10 Invalid-Sensor {UserNoticeConfig.EVENT_TYPES.value[0]} [openvpn] openvpn server 'someserver' user 'someuser' address '*******:80' - connecting"

        object_name, parsed_ln = parse_ln(
            ln,
            UserNoticeConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        ln = f"2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [openvpn] openvpn server 'someserver' user 'someuser' address '*******:80' - connecting"

        object_name, parsed_ln = parse_ln(
            ln,
            UserNoticeConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        ln = f"2023-05-12 23:36:10 Sensor-1 {UserNoticeConfig.EVENT_TYPES.value[0]} [invalid-alert-type] openvpn server 'someserver' user 'someuser' address '*******:80' - connecting"

        object_name, parsed_ln = parse_ln(
            ln,
            UserNoticeConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            UserNoticeConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnUserWarning(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.sensor_list_of_names_and_addresses = sensor_list_of_names_and_addresses
        self.sensor_dict_of_addresses_and_names = sensor_dict_of_addresses_and_names

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        for e_t in UserWarningConfig.EVENT_TYPES.value:
            ln = f'2023-05-12 23:36:10 Sensor-1 {e_t} [dpinger] GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%'

            object_name, parsed_ln = parse_ln(
                ln,
                UserWarningConfig,
                self.sensor_list_of_names_and_addresses,
                self.sensor_dict_of_addresses_and_names
            )

            self.assertEqual(object_name, 'Sensor-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], '[dpinger]')
            self.assertEqual(parsed_ln[3], 'GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        for e_t in UserWarningConfig.EVENT_TYPES.value:
            ln = f'2023-05-12 23:36:10 *********** {e_t} [dpinger] GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%'

            object_name, parsed_ln = parse_ln(
                ln,
                UserWarningConfig,
                self.sensor_list_of_names_and_addresses,
                self.sensor_dict_of_addresses_and_names
            )

            self.assertEqual(object_name, 'Sensor-1')

            self.assertEqual(parsed_ln[0], '2023-05-12')
            self.assertEqual(parsed_ln[1], '23:36:10')
            self.assertEqual(parsed_ln[2], '[dpinger]')
            self.assertEqual(parsed_ln[3], 'GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%')

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = f'2023-05-12 23:36:10 Invalid-Sensor {UserWarningConfig.EVENT_TYPES.value[0]} [dpinger] GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%'

        object_name, parsed_ln = parse_ln(
            ln,
            UserWarningConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        ln = '2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [dpinger] GRETUN_TUNNELV4 ***********: Alarm latency 0us stddev 0us loss 100%'

        object_name, parsed_ln = parse_ln(
            ln,
            UserWarningConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        pass

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            UserWarningConfig,
            self.sensor_list_of_names_and_addresses,
            self.sensor_dict_of_addresses_and_names
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnVMware(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.vmware_list_of_names_and_addresses = vmware_list_of_names_and_addresses
        self.vmware_dict_of_addresses_and_names = vmware_dict_of_addresses_and_names

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        ln = '2023-05-12 23:36:10 VMware-1 (local0/info) [vsan-health-main] 2024-11-16T00:00:18.056+03:30 INFO vsan-mgmt[11589] [VsanPyVmomiProfiler::log opID=noOpId] Profiler:'

        object_name, parsed_ln = parse_ln(
            ln,
            VMwareConfig,
            self.vmware_list_of_names_and_addresses,
            self.vmware_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, 'VMware-1')

        self.assertEqual(parsed_ln[0], '2023-05-12')
        self.assertEqual(parsed_ln[1], '23:36:10')
        self.assertEqual(parsed_ln[2], '(local0/info)')
        self.assertEqual(parsed_ln[3], '[vsan-health-main]')
        self.assertEqual(parsed_ln[4], '2024-11-16T00:00:18.056+03:30 INFO vsan-mgmt[11589] [VsanPyVmomiProfiler::log opID=noOpId] Profiler:')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        ln = '2023-05-12 23:36:10 *********** (local0/info) [vsan-health-main] 2024-11-16T00:00:18.056+03:30 INFO vsan-mgmt[11589] [VsanPyVmomiProfiler::log opID=noOpId] Profiler:'

        object_name, parsed_ln = parse_ln(
            ln,
            VMwareConfig,
            self.vmware_list_of_names_and_addresses,
            self.vmware_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, 'VMware-1')

        self.assertEqual(parsed_ln[0], '2023-05-12')
        self.assertEqual(parsed_ln[1], '23:36:10')
        self.assertEqual(parsed_ln[2], '(local0/info)')
        self.assertEqual(parsed_ln[3], '[vsan-health-main]')
        self.assertEqual(parsed_ln[4], '2024-11-16T00:00:18.056+03:30 INFO vsan-mgmt[11589] [VsanPyVmomiProfiler::log opID=noOpId] Profiler:')

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = '2023-05-12 23:36:10 Invalid-VMware (local0/info) [vsan-health-main] 2024-11-16T00:00:18.056+03:30 INFO vsan-mgmt[11589] [VsanPyVmomiProfiler::log opID=noOpId] Profiler:'

        object_name, parsed_ln = parse_ln(
            ln,
            VMwareConfig,
            self.vmware_list_of_names_and_addresses,
            self.vmware_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        pass

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        pass

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            VMwareConfig,
            self.vmware_list_of_names_and_addresses,
            self.vmware_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnVPNServer(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        pass

    def test_valid_line(self):
        '''
        Test parsing a valid log line.
        '''
        ln = r'2023-05-12 23:36:10 VPNSERVER (user/info) [MSWinEventLog	1	System	5061875	Mon] Jun 30 20:28:56 2025       20272   RemoteAccess    N/A     N/A     Information     VPNSERVER.sth.local     N/A         RoutingDomainID- {********-0000-0000-0000-********0000}: CoID={NA}: The user MYDOMAIN\n.peterson connected on port VPN1-123 on 6/30/2025 at 5:36 PM and disconnected on 6/30/2025 at 8:28 PM.  The user was active for 172 minutes 12 seconds.  ******** bytes were sent and 7613881 bytes were received. The reason for disconnecting was user request. The tunnel used was WAN Miniport (PPTP). The quarantine state was .      654321'

        object_name, parsed_ln = parse_ln(
            ln,
            VPNServerConfig,
            None,
            None,
        )

        self.assertEqual(object_name, None)

        self.assertEqual(parsed_ln[0], '2023-05-12')
        self.assertEqual(parsed_ln[1], '23:36:10')
        self.assertEqual(parsed_ln[2], 'MYDOMAIN')
        self.assertEqual(parsed_ln[3], 'n.peterson')
        self.assertEqual(parsed_ln[4], 'VPN1-123')
        self.assertEqual(parsed_ln[5], '172 minutes 12 seconds')
        self.assertEqual(parsed_ln[6], '********')
        self.assertEqual(parsed_ln[7], '7613881')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        pass

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        pass

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        pass

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        pass

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        ln = '2023-05-12 23:36:10 This is an invalid line'

        object_name, parsed_ln = parse_ln(
            ln,
            VPNServerConfig,
            None,
            None,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

class TestParseLnWindowsServer(TestCase):
    '''
    Tests for the parse_ln function for logs lines.

    This test suite verifies that the function correctly parses log lines
    and extracts relevant information for logs lines.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.windowsserver_list_of_names_and_addresses = windowsserver_list_of_names_and_addresses
        self.windowsserver_dict_of_addresses_and_names = windowsserver_dict_of_addresses_and_names

    def test_valid_line__ws_an_ad_pattern(self):
        '''
        Test parsing a valid log line
        that matches WS_AN_AD_PATTERN pettern
        '''
        ln = '2023-05-12 23:36:10 WindowsServer-1 (user/info) [MSWinEventLog	1	Security	317081	Sat] Dec 07 00:00:22 2024	4634	Microsoft-Windows-Security-Auditing	N/A	N/A	Success Audit	sth.sth.local	Logoff		An account was logged off.    Subject:   Security ID:  S-1-5-18   Account Name:  FooName$   Account Domain:  BarDomain   Logon ID:  0x2B3F21    Logon Type:   3    This event is generated when a logon session is destroyed. It may be positively correlated with a logon event using the Logon ID value. Logon IDs are only unique between reboots on the same computer.	********'

        object_name, parsed_ln = parse_ln(
            ln,
            WindowsServerConfig,
            self.windowsserver_list_of_names_and_addresses,
            self.windowsserver_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, 'WindowsServer-1')

        self.assertEqual(parsed_ln[0], '2023-05-12')
        self.assertEqual(parsed_ln[1], '23:36:10')
        self.assertEqual(parsed_ln[2], '[MSWinEventLog	1	Security	317081	Sat]')
        self.assertEqual(parsed_ln[3], 'Dec 07 00:00:22 2024	4634	Microsoft-Windows-Security-Auditing	N/A	N/A	Success Audit	sth.sth.local	Logoff		An account was logged off.    Subject:   Security ID:  S-1-5-18   Account Name:  FooName$   Account Domain:  BarDomain   Logon ID:  0x2B3F21    Logon Type:   3    This event is generated when a logon session is destroyed. It may be positively correlated with a logon event using the Logon ID value. Logon IDs are only unique between reboots on the same computer.	********'.replace('\t', ' '))
        self.assertEqual(parsed_ln[4], '4634')
        self.assertEqual(parsed_ln[5], 'Logon/Logoff')
        self.assertEqual(parsed_ln[6], 'Low')
        self.assertEqual(parsed_ln[7], 'FooName$')
        self.assertEqual(parsed_ln[8], 'BarDomain')
        self.assertEqual(parsed_ln[9], '')

    def test_valid_line__ws_sw_pattern(self):
        '''
        Test parsing a valid log line
        that matches WS_SW_PATTERN pattern
        '''
        ln = '2023-05-12 23:36:10 WindowsServer-1 (user/info) [MSWinEventLog	1	Security	317081	Sat] Dec 07 00:00:22 2024	4634    Microsoft-Windows-Security-Auditing     N/A     N/A     Success Audit      sth.sth.local      Credential Validation           The computer attempted to validate the credentials for an account.    Authentication Package: MICROSOFT_AUTHENTICATION_PACKAGE_V1_0  Logon Account: SOMEADDS$  Source Workstation: SOMEADDS  Error Code: 0x0 4106884'

        object_name, parsed_ln = parse_ln(
            ln,
            WindowsServerConfig,
            self.windowsserver_list_of_names_and_addresses,
            self.windowsserver_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, 'WindowsServer-1')

        self.assertEqual(parsed_ln[0], '2023-05-12')
        self.assertEqual(parsed_ln[1], '23:36:10')
        self.assertEqual(parsed_ln[2], '[MSWinEventLog	1	Security	317081	Sat]')
        self.assertEqual(parsed_ln[3], 'Dec 07 00:00:22 2024	4634    Microsoft-Windows-Security-Auditing     N/A     N/A     Success Audit      sth.sth.local      Credential Validation           The computer attempted to validate the credentials for an account.    Authentication Package: MICROSOFT_AUTHENTICATION_PACKAGE_V1_0  Logon Account: SOMEADDS$  Source Workstation: SOMEADDS  Error Code: 0x0 4106884'.replace('\t', ' '))
        self.assertEqual(parsed_ln[4], '4634')
        self.assertEqual(parsed_ln[5], 'Logon/Logoff')
        self.assertEqual(parsed_ln[6], 'Low')
        self.assertEqual(parsed_ln[7], '')
        self.assertEqual(parsed_ln[8], '')
        self.assertEqual(parsed_ln[9], 'SOMEADDS')

    def test_mapping_object_ip_to_object_name(self):
        '''
        Test if object IP is correctly mapped to object name.
        '''
        ln = '2023-05-12 23:36:10 *********** (user/info) [MSWinEventLog	1	Security	317081	Sat] Dec 07 00:00:22 2024	4634	Microsoft-Windows-Security-Auditing	N/A	N/A	Success Audit	sth.sth.local	Logoff		An account was logged off.    Subject:   Security ID:  S-1-5-18   Account Name:  FooName$   Account Domain:  BarDomain   Logon ID:  0x2B3F21    Logon Type:   3    This event is generated when a logon session is destroyed. It may be positively correlated with a logon event using the Logon ID value. Logon IDs are only unique between reboots on the same computer.	********'

        object_name, parsed_ln = parse_ln(
            ln,
            WindowsServerConfig,
            self.windowsserver_list_of_names_and_addresses,
            self.windowsserver_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, 'WindowsServer-1')

        self.assertEqual(parsed_ln[0], '2023-05-12')
        self.assertEqual(parsed_ln[1], '23:36:10')
        self.assertEqual(parsed_ln[2], '[MSWinEventLog	1	Security	317081	Sat]')
        self.assertEqual(parsed_ln[3], 'Dec 07 00:00:22 2024	4634	Microsoft-Windows-Security-Auditing	N/A	N/A	Success Audit	sth.sth.local	Logoff		An account was logged off.    Subject:   Security ID:  S-1-5-18   Account Name:  FooName$   Account Domain:  BarDomain   Logon ID:  0x2B3F21    Logon Type:   3    This event is generated when a logon session is destroyed. It may be positively correlated with a logon event using the Logon ID value. Logon IDs are only unique between reboots on the same computer.	********'.replace('\t', ' '))
        self.assertEqual(parsed_ln[4], '4634')
        self.assertEqual(parsed_ln[5], 'Logon/Logoff')
        self.assertEqual(parsed_ln[6], 'Low')
        self.assertEqual(parsed_ln[7], 'FooName$')
        self.assertEqual(parsed_ln[8], 'BarDomain')
        self.assertEqual(parsed_ln[9], '')

    def test_invalid_object_name(self):
        '''
        Test parsing a line with an invalid object name.
        '''
        ln = '2023-05-12 23:36:10 Invalid-WindowsServer (user/info) [MSWinEventLog	1	Security	317081	Sat] Dec 07 00:00:22 2024	4634	Microsoft-Windows-Security-Auditing	N/A	N/A	Success Audit	sth.sth.local	Logoff		An account was logged off.    Subject:   Security ID:  S-1-5-18   Account Name:  FooName$   Account Domain:  BarDomain   Logon ID:  0x2B3F21    Logon Type:   3    This event is generated when a logon session is destroyed. It may be positively correlated with a logon event using the Logon ID value. Logon IDs are only unique between reboots on the same computer.	********'

        object_name, parsed_ln = parse_ln(
            ln,
            WindowsServerConfig,
            self.windowsserver_list_of_names_and_addresses,
            self.windowsserver_dict_of_addresses_and_names,
        )

        self.assertEqual(object_name, None)
        self.assertEqual(parsed_ln, None)

    def test_invalid_event_type(self):
        '''
        Test parsing a line with an invalid event type
        '''
        pass

    def test_invalid_alert_type(self):
        '''
        Test parsing a line with an invalid alert type
        '''
        pass

    def test_invalid_line(self):
        '''
        Test parsing an invalid line.
        '''
        for ln in [
            '2023-05-12 23:36:10 This is an invalid line',

            ## ln has [dns]
            '2023-05-12 23:36:10 WindowsServer-1 (user/notice) [MSWinEventLog    1       N/A     10044897        Sat] Jul 12 00:00:22 2025       N/A     N/A     N/A     N/A     N/A     N/A     N/A             [dns] 7/12/2025 12:00:22 AM 1090 PACKET  00000163D7FB3D40 UDP Rcv *******    ee38   Q [1000       NOERROR] A      (8)sth(10)example(2)ir(0)     N/A',

            ## ln has [dhcp]
            '2023-05-12 23:36:10 WindowsServer-1 (user/notice) [MSWinEventLog       1       N/A     206882  Sat] Jul 12 12:52:41 2025       N/A     N/A     N/A     N/A     N/A     N/A     N/A             [dhcp] 31,07/12/25,12:52:41,DNS Update Failed,***********,sth.sth.local,,,0,6,,,,,,,,,9004       N/A',
        ]:
            object_name, parsed_ln = parse_ln(
                ln,
                WindowsServerConfig,
                self.windowsserver_list_of_names_and_addresses,
                self.windowsserver_dict_of_addresses_and_names,
            )

            self.assertEqual(object_name, None)
            self.assertEqual(parsed_ln, None)

class TestGetNoOfInfiles(TestCase):
    '''
    Tests for the get_no_of_infiles function.

    This test suite verifies that the function correctly calculates the number
    of input files needed based on the given length and chunk size.
    '''

    def test_zero_length(self):
        '''
        Test that zero length returns zero files.
        '''
        result = get_no_of_infiles(0)
        self.assertEqual(result, 0)

    def test_length_less_than_chunk_size(self):
        '''
        Test that length less than chunk size returns one file.
        '''
        # Temporarily store original value
        original_infile_chunksize = MYSQLConfig.INFILE_CHUNKSIZE.value

        result = get_no_of_infiles(original_infile_chunksize-1)
        self.assertEqual(result, 1)

    def test_length_equal_to_chunk_size(self):
        '''
        Test that length equal to chunk size returns one file.
        '''
        # Temporarily store original value
        original_infile_chunksize = MYSQLConfig.INFILE_CHUNKSIZE.value

        result = get_no_of_infiles(original_infile_chunksize)
        self.assertEqual(result, 1)

    def test_length_greater_than_chunk_size(self):
        '''
        Test that length greater than chunk size returns correct number of files.
        '''
        # Temporarily store original value
        original_infile_chunksize = MYSQLConfig.INFILE_CHUNKSIZE.value

        result = get_no_of_infiles(original_infile_chunksize+1)
        self.assertEqual(result, 2)

        result = get_no_of_infiles(original_infile_chunksize * 19)
        self.assertEqual(result, 19)

class TestCreateNameOfIndex(TestCase):
    '''
    Tests for the create_name_of_index function.

    This test suite verifies that the function correctly sanitizes input strings
    and generates appropriate index names.
    '''

    def test_simple_key(self):
        '''
        Test that a simple key is properly converted to lowercase and appended with _index.
        '''
        result = create_name_of_index('Domain')
        self.assertEqual(result, 'domain_index')

    def test_key_with_spaces(self):
        '''
        Test that spaces are properly removed from the key.
        '''
        result = create_name_of_index('Source IP')
        self.assertEqual(result, 'sourceip_index')

    def test_key_with_special_characters(self):
        '''
        Test that special characters are properly removed from the key.
        '''
        result = create_name_of_index('GID:SID')
        self.assertEqual(result, 'gidsid_index')

        result = create_name_of_index('User-Agent')
        self.assertEqual(result, 'useragent_index')

        result = create_name_of_index('HTTP/1.1')
        self.assertEqual(result, 'http11_index')

    def test_key_with_mixed_case(self):
        '''
        Test that mixed case is properly converted to lowercase.
        '''
        result = create_name_of_index('SourceIP')
        self.assertEqual(result, 'sourceip_index')

        result = create_name_of_index('CamelCaseKey')
        self.assertEqual(result, 'camelcasekey_index')

    def test_empty_key(self):
        '''
        Test that an empty key returns just '_index'.
        '''
        result = create_name_of_index('')
        self.assertEqual(result, '_index')

class TestCreatePathOfInfile(TestCase):
    '''
    Tests for the create_path_of_infile function.

    This test suite verifies that the function correctly generates file paths
    for infiles based on the provided parameters.
    '''

    def test_basic_path_generation(self):
        '''
        Test that a basic path is correctly generated without a chunk number.
        '''
        result = create_path_of_infile('my_database', 'my_table')
        self.assertEqual(result, '/tmp/infile__my_database__my_table.csv')

    def test_path_with_chunk_number(self):
        '''
        Test that a path with a chunk number is correctly generated.
        '''
        result = create_path_of_infile('my_database', 'my_table', 1)
        self.assertEqual(result, '/tmp/infile__my_database__my_table__chunk_1.csv')

        result = create_path_of_infile('my_database', 'my_table', 42)
        self.assertEqual(result, '/tmp/infile__my_database__my_table__chunk_42.csv')

    def test_with_special_characters_in_names(self):
        '''
        Test that the function handles database and table names with special characters.
        '''
        result = create_path_of_infile('database-name', 'table_name')
        self.assertEqual(result, '/tmp/infile__database-name__table_name.csv')

        result = create_path_of_infile('daemon__Sensor_1__2024_11_30', 'complex-table')
        self.assertEqual(result, '/tmp/infile__daemon__Sensor_1__2024_11_30__complex-table.csv')

    def test_with_zero_chunk_number(self):
        '''
        Test that the function handles a zero chunk number correctly.
        '''
        result = create_path_of_infile('my_database', 'my_table', 0)
        self.assertEqual(result, '/tmp/infile__my_database__my_table.csv')

class TestDashToUnderscore(TestCase):
    '''
    Tests for the dash_to_underscore function.

    This test suite verifies that the function correctly converts dashes to underscores
    in strings and handles edge cases appropriately.
    '''

    def test_simple_string(self):
        '''
        Test that a string with dashes is properly converted to underscores.
        '''
        result = dash_to_underscore('Sensor-1')
        self.assertEqual(result, 'Sensor_1')

    def test_date_string(self):
        '''
        Test that a date string with dashes is properly converted to underscores.
        '''
        result = dash_to_underscore('2024-11-30')
        self.assertEqual(result, '2024_11_30')

    def test_multiple_dashes(self):
        '''
        Test that a string with multiple dashes is properly converted.
        '''
        result = dash_to_underscore('this-is-a-test-string')
        self.assertEqual(result, 'this_is_a_test_string')

    def test_no_dashes(self):
        '''
        Test that a string without dashes remains unchanged.
        '''
        result = dash_to_underscore('NoChangeNeeded')
        self.assertEqual(result, 'NoChangeNeeded')

    def test_empty_string(self):
        '''
        Test that an empty string returns an empty string.
        '''
        result = dash_to_underscore('')
        self.assertEqual(result, '')

class TestUnderscoreToDash(TestCase):
    '''
    Tests for the underscore_to_dash function.

    This test suite verifies that the function correctly converts underscores to dashes
    in strings and handles edge cases appropriately.
    '''

    def test_simple_string(self):
        '''
        Test that a string with underscores is properly converted to dashes.
        '''
        result = underscore_to_dash('Sensor_1')
        self.assertEqual(result, 'Sensor-1')

    def test_date_string(self):
        '''
        Test that a date string with underscores is properly converted to dashes.
        '''
        result = underscore_to_dash('2024_11_30')
        self.assertEqual(result, '2024-11-30')

    def test_multiple_underscores(self):
        '''
        Test that multiple underscores in a string are all converted to dashes.
        '''
        result = underscore_to_dash('daemon__Sensor_1__2024_11_30')
        self.assertEqual(result, 'daemon--Sensor-1--2024-11-30')

    def test_empty_string(self):
        '''
        Test that an empty string returns an empty string.
        '''
        result = underscore_to_dash('')
        self.assertEqual(result, '')

    def test_string_without_underscores(self):
        '''
        Test that a string without underscores remains unchanged.
        '''
        result = underscore_to_dash('SensorOne')
        self.assertEqual(result, 'SensorOne')

class TestBreakNameOfDatabase(TestCase):
    '''
    Tests for the break_name_of_database function.

    This test suite verifies that the function correctly breaks down database names
    into their component parts: slug, object name, and date.
    '''

    def test_non_dated_database(self):
        '''
        Test that a non-dated database name returns the correct components.
        '''
        result = break_name_of_database('geolocation')
        self.assertEqual(result, ('geolocation', '', ''))

        result = break_name_of_database('malicious')
        self.assertEqual(result, ('malicious', '', ''))

    def test_two_part_database_name(self):
        '''
        Test that a two-part database name (slug and date) returns the correct components.
        '''
        result = break_name_of_database('dhcp__2024_11_30')
        self.assertEqual(result, ('dhcp', '', '2024_11_30'))

        result = break_name_of_database('dns__2023_01_15')
        self.assertEqual(result, ('dns', '', '2023_01_15'))

    def test_three_part_database_name(self):
        '''
        Test that a three-part database name (slug, object name, and date) returns the correct components.
        '''
        result = break_name_of_database('daemon__Sensor_1__2024_11_30')
        self.assertEqual(result, ('daemon', 'Sensor_1', '2024_11_30'))

        result = break_name_of_database('logs__Server_Main__2023_05_20')
        self.assertEqual(result, ('logs', 'Server_Main', '2023_05_20'))

    def test_invalid_database_name(self):
        '''
        Test that an invalid database name format returns empty strings for all components.
        '''
        result = break_name_of_database('invalid_format_database')
        self.assertEqual(result, ('', '', ''))

        result = break_name_of_database('too__many__separators__in__name')
        self.assertEqual(result, ('', '', ''))

        result = break_name_of_database('')
        self.assertEqual(result, ('', '', ''))

class TestGetDirectoryPathFromNameOfDatabase(TestCase):
    '''
    Tests for the get_directory_path_from_name_of_database function.

    This test suite verifies that the function correctly converts database names
    to their corresponding directory paths based on different patterns.
    '''

    @patch('base.utils.GeoLocationConfig')
    def test_geolocation_database(self, mock_geo_vars):
        '''
        Test that the geolocation database name returns the correct path.
        '''
        mock_geo_vars.SLUG.value = 'geolocation'
        mock_geo_vars.get_logs_parsed_dir.return_value = '/FOO/BAR/BAZ/geolocation'

        result = get_directory_path_from_name_of_database('geolocation')
        self.assertEqual(result, '/FOO/BAR/BAZ/geolocation')
        mock_geo_vars.get_logs_parsed_dir.assert_called_once()

    @patch('base.utils.MaliciousConfig')
    def test_malicious_database(self, mock_malicious_vars):
        '''
        Test that the malicious database name returns the correct path.
        '''
        mock_malicious_vars.SLUG.value = 'malicious'
        mock_malicious_vars.get_logs_parsed_dir.return_value = '/FOO/BAR/BAZ/malicious'

        result = get_directory_path_from_name_of_database('malicious')
        self.assertEqual(result, '/FOO/BAR/BAZ/malicious')
        mock_malicious_vars.get_logs_parsed_dir.assert_called_once()

    @patch('base.utils.settings')
    @patch('base.utils.break_name_of_database')
    def test_database_with_date_only(self, mock_break_name, mock_settings):
        '''
        Test that a database name with only a slug and date returns the correct path.
        '''
        mock_settings.LOGS_PARSED_DIR = '/FOO/BAR/BAZ'
        mock_break_name.return_value = ('dhcp', '', '2024_11_30')

        result = get_directory_path_from_name_of_database('dhcp__2024_11_30')
        self.assertEqual(result, '/FOO/BAR/BAZ/dhcp/2024-11-30')
        mock_break_name.assert_called_once_with('dhcp__2024_11_30')

    @patch('base.utils.settings')
    @patch('base.utils.break_name_of_database')
    def test_database_with_object_name_and_date(self, mock_break_name, mock_settings):
        '''
        Test that a database name with slug, object name, and date returns the correct path.
        '''
        mock_settings.LOGS_PARSED_DIR = '/FOO/BAR/BAZ'
        mock_break_name.return_value = ('daemon', 'Sensor_1', '2024_11_30')

        result = get_directory_path_from_name_of_database('daemon__Sensor_1__2024_11_30')
        self.assertEqual(result, '/FOO/BAR/BAZ/daemon/Sensor-1/2024-11-30')
        mock_break_name.assert_called_once_with('daemon__Sensor_1__2024_11_30')

    @patch('base.utils.break_name_of_database')
    def test_invalid_database_name(self, mock_break_name):
        '''
        Test that an invalid database name returns None.
        '''
        mock_break_name.return_value = ('', '', '')

        result = get_directory_path_from_name_of_database('unknown_database')
        self.assertIsNone(result)
        mock_break_name.assert_called_once_with('unknown_database')

class TestCreateNameOfDatabase(TestCase):
    '''
    Tests for the create_name_of_database function.

    This test suite verifies that the function correctly generates database names
    based on the provided slug, date, and object name.
    '''

    def test_non_dated_database(self):
        '''
        Test that a non-dated database slug returns just the slug.
        '''
        non_dated_databases = MYSQLConfig.NON_DATED_DATABASES.value

        for ndd in non_dated_databases:
            result = create_name_of_database(ndd)
            self.assertEqual(result, ndd)

            result = create_name_of_database(ndd, '2024-11-30')
            self.assertEqual(result, ndd)

            result = create_name_of_database(ndd, '2024-11-30', 'Sensor-1')
            self.assertEqual(result, ndd)

    def test_with_date_only(self):
        '''
        Test that a slug with date generates the correct database name.
        '''
        result = create_name_of_database('dhcp', '2024-11-30')
        self.assertEqual(result, 'dhcp__2024_11_30')

        result = create_name_of_database('dns', '2023-01-15')
        self.assertEqual(result, 'dns__2023_01_15')

    def test_with_date_and_object_name(self):
        '''
        Test that a slug with date and object name generates the correct database name.
        '''
        result = create_name_of_database('daemon', '2024-11-30', 'Sensor-1')
        self.assertEqual(result, 'daemon__Sensor_1__2024_11_30')

        result = create_name_of_database('snort', '2024-11-30', 'Sensor-2')
        self.assertEqual(result, 'snort__Sensor_2__2024_11_30')
