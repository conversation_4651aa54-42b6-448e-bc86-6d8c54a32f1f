'''
functions that query MYSQL databases
'''

from typing import <PERSON><PERSON>, Union, List, Dict, Any

from MySQLdb import connect
from rahavard import (
    convert_byte,
    sort_dict,
)

from .utils_classes import (
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    MYSQLConfig,
    SnortConfig,
    SquidConfig,
)

from base.utils_ip import (
    is_ip,
    is_private,
)

from .utils import (
    list_of_tuples_to_list,
)


def get_indexes_of_table(
    database_name: str,
    table_name: str,
    exclude_columns: List[str] = ['ID']
) -> List[Tuple[Any, ...]]:
    '''
    Retrieve the indexes of a specified table in a MySQL database, optionally excluding specified columns.

    Args:
        database_name (str): The name of the database.
        table_name (str): The name of the table.
        exclude_columns (list, optional): A list of column names to exclude from the index retrieval. Defaults to ['ID'].

    Returns:
        list: A list of tuples representing the indexes of the table. Each tuple contains index information.
    '''

    if exclude_columns:
        ## __PLACEHOLDERS_FOR_WHERE__
        placeholders = ', '.join(['%s'] * len(exclude_columns))
        where_clause = f'WHERE (column_name NOT IN ({placeholders}))'
    else:
        ## make sure it's a list
        ## to prevent errors when executing query
        exclude_columns = []

        where_clause = ''

    with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
        with conn.cursor() as cur:
            cur.execute(f'''
                SHOW INDEX FROM {table_name}
                {where_clause}
            ;''', tuple(exclude_columns))
            return cur.fetchall()
            ## ((...), (...)) OR ((...),)

def get_all_ips(unique: bool, private_only: bool = False, public_only: bool = False) -> List[str]:
    '''
    Retrieve a list of IP addresses from snort, filterlog, squid, dhcp and dns databases.

    This function queries multiple databases to retrieve IP addresses from specified tables and columns.
    It can filter the results to return only unique IPs, private IPs, or public IPs.

    This function discards non-ips, like '', '-', etc.

    Args:
        unique (bool): If True, return only unique IP addresses.
        private_only (bool, optional): If True, return only private IP addresses. Defaults to False.
        public_only (bool, optional): If True, return only public IP addresses. Defaults to False.

    Returns:
        list: A list of IP addresses based on the specified filters.
    '''

    all_dbs = get_databases(include_builtins=False)

    if not all_dbs:
        return []

    big_list = [
        [
            SnortConfig,
            [
                ## table_name              column/key
                ('sourceiptoptable',      '`Source IP`'),
                ('destinationiptoptable', '`Destination IP`'),
            ],
        ],
        [
            FilterLogConfig,
            [
                ('sourceiptoptable',      '`Source IP`'),
                ('destinationiptoptable', '`Destination IP`'),
            ],
        ],
        [
            SquidConfig,
            [
                ('sourceiptoptable',      '`Source IP`'),
                ('destinationiptoptable', '`Destination IP`'),
            ],
        ],
        [
            DHCPConfig,
            [
                ('sourceiptoptable', '`Source IP`'),
            ],
        ],
        [
            DNSConfig,
            [
                ('sourceiptoptable', '`Source IP`'),
            ],
        ],
    ]

    ips: List[str] = []

    for class_, list_ in big_list:
        databases = [
            _ for _ in all_dbs
            if class_.SLUG.value in _
        ]

        for table_name, key in list_:
            for database_name in databases:
                try:
                    with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
                        with conn.cursor() as cur:
                            cur.execute(f'SELECT {key} FROM {table_name};')
                            ips.extend(list_of_tuples_to_list(cur.fetchall()))
                except Exception:
                    pass

    if unique:
        ips = set(ips)

    ## discard non-ips
    ips = [
        _ for _ in ips
        if is_ip(_)
    ]

    if private_only:
        ips = [
            _ for _ in ips
            if is_private(_)
        ]
    elif public_only:
        ips = [
            _ for _ in ips
            if not is_private(_)
        ]

    return ips

def get_all_domains(unique: bool) -> List[str]:
    '''
    Retrieve all domain names from squid and dns databases.

    This function connects to multiple databases and retrieves domain names
    from specified tables and columns. It supports filtering for unique domain
    names if requested.

    Args:
        unique (bool): If True, return only unique domain names.

    Returns:
        list: A list of domain names retrieved from the databases.
    '''

    all_dbs = get_databases(include_builtins=False)

    if not all_dbs:
        return []

    big_list = [
        [
            SquidConfig,
            [
                ## table_name    column/key
                ('urltoptable', 'URL'),
            ],
        ],
        [
            DNSConfig,
            [
                ('questionnametoptable', '`Question Name`'),
            ],
        ],
    ]

    domains: List[str] = []

    for class_, list_ in big_list:
        databases = [
            _ for _ in all_dbs
            if class_.SLUG.value in _
        ]

        for table_name, key in list_:
            for database_name in databases:
                try:
                    with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
                        with conn.cursor() as cur:
                            cur.execute(f'SELECT {key} FROM {table_name};')
                            domains.extend(list_of_tuples_to_list(cur.fetchall()))
                except Exception:
                    pass

    if unique:
        domains = set(domains)

    return list(domains)

def database_exists(database_name: str) -> bool:
    '''
    Note:
        - https://stackoverflow.com/a/838993/
    '''

    with connect(**MYSQLConfig.R_USER_CREDS.value) as conn:
        with conn.cursor() as cur:
            cur.execute('''
                SELECT SCHEMA_NAME
                FROM INFORMATION_SCHEMA.SCHEMATA
                WHERE SCHEMA_NAME = %s

            ;''', (database_name,))
            if cur.fetchone():
                return True
            return False

def table_exists(database_name: str, table_name: str) -> bool:
    return table_name in get_tables(database_name)

def get_max_id(database_name: str, table_name: str) -> int:
    try:
        with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                ## no placeholders/parameterizing for identifiers
                ## like database names, table names, or column names
                cur.execute(f'SELECT MAX(ID) FROM `{table_name}`;')
                max_id = cur.fetchone()
                if max_id and max_id[0] is not None:
                    return max_id[0]
                return 0
    except:
        return 0

def get_databases(include_builtins: bool = False) -> List[str]:
    with connect(**MYSQLConfig.R_USER_CREDS.value) as conn:
        with conn.cursor() as cur:
            cur.execute('SHOW DATABASES;')
            databases = list_of_tuples_to_list(cur.fetchall())
            ## '--> ['information_schema', 'malicious', 'performance_schema', ... ]

    if not include_builtins:
        databases = [
            _ for _ in databases
            if _ not in MYSQLConfig.BUILTIN_DATABASES.value
        ]

    return databases

def get_databases_and_sizes(
    include_builtins: bool = False,
    sort: bool = True,
    based_on: str = '',
    reverse: bool = True,
    convert: bool = False,
) -> Dict[str, Union[int, str]]:
    dictionary: Dict[str, int] = {}

    for db in get_databases(include_builtins=include_builtins):
        ## passing convert as False so we can:
        ##   properly sort dictionary first, and
        ##   later decide whether to convert size when returning
        dictionary[db] = get_size_of_database(db, convert=False)

    if sort:
        dictionary = sort_dict(dictionary, based_on=based_on, reverse=reverse)

    if convert:
        dictionary = {
            db: convert_byte(size)
            for db, size in dictionary.items()
        }

    return dictionary

def get_size_of_database(database_name: str, convert: bool = False) -> Union[int, str]:
    '''
    Note:
        - https://stackoverflow.com/a/46155036/
    '''
    ## __HAS_RUST_VERSION__

    with connect(**MYSQLConfig.R_USER_CREDS.value) as conn:
        with conn.cursor() as cur:
            cur.execute('''
                SELECT SUM((DATA_LENGTH + INDEX_LENGTH))
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_SCHEMA = %s
            ;''', (database_name,))
            size_in_bytes = cur.fetchone()[0] or 0  ## Decimal('13238272')

    if convert:
        return convert_byte(int(size_in_bytes))  ## 12.7MB

    return int(size_in_bytes)  ## 13283647

def get_size_of_table(database_name: str, table_name: str, convert: bool = False) -> Union[int, str]:
    ## __HAS_RUST_VERSION__

    with connect(**MYSQLConfig.R_USER_CREDS.value) as conn:
        with conn.cursor() as cur:
            cur.execute('''
                SELECT SUM((DATA_LENGTH + INDEX_LENGTH))
                FROM INFORMATION_SCHEMA.TABLES
                WHERE (
                  TABLE_SCHEMA = %s AND
                  TABLE_NAME = %s
                )
            ;''', (database_name, table_name))
            size_in_bytes = cur.fetchone()[0] or 0  ## Decimal('13238272')

    if convert:
        return convert_byte(int(size_in_bytes))  ## 12.7MB

    return int(size_in_bytes)  ## 13283647

def get_tables(database_name: str) -> List[str]:
    ## __HAS_RUST_VERSION__

    with connect(**MYSQLConfig.R_USER_CREDS.value) as conn:
        with conn.cursor() as cur:
            cur.execute('''
                SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_SCHEMA = %s
            ;''', (database_name,))
            return list_of_tuples_to_list(cur.fetchall())
            ## '--> ['snorttable', 'sourceiptoptable', ...]

def get_tables_and_sizes(
    database_name: str,
    sort: bool = True,
    based_on: str = '',
    reverse: bool = True,
    convert: bool = False,
) -> Dict[str, Union[int, str]]:
    ## __HAS_RUST_VERSION__

    dictionary: Dict[str, int] = {}

    for table in get_tables(database_name):
        ## passing convert as False so we can:
        ##   properly sort dictionary first, and
        ##   later decide whether to convert size when returning
        dictionary[table] = get_size_of_table(database_name, table, convert=False)

    if sort:
        dictionary = sort_dict(dictionary, based_on=based_on, reverse=reverse)

    if convert:
        return {
            table: convert_byte(size)
            for table, size in dictionary.items()
        }

    return dictionary
